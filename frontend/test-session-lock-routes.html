<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Lock Route Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .route-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .route-card {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .route-card:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .route-card.public {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .route-card.protected {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .route-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .route-card p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .route-card .status {
            font-weight: bold;
            margin-top: 10px;
        }
        .route-card.public .status {
            color: #155724;
        }
        .route-card.protected .status {
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Session Lock Route Test</h1>
    <p>This test demonstrates how the session lock modal behaves on different types of pages.</p>

    <div class="test-section">
        <h2>How It Works</h2>
        <p><strong>Session Lock Modal Logic:</strong></p>
        <ul>
            <li>✅ <strong>Shows on Protected Pages:</strong> /dashboard, /profile, /admin (when authenticated)</li>
            <li>❌ <strong>Hidden on Public Pages:</strong> /, /about, /services, /contact, /login, /register</li>
            <li>🔐 <strong>Only for Authenticated Users:</strong> No point showing lock modal for non-logged users</li>
            <li>📍 <strong>Route-Aware:</strong> Uses Vue Router meta and path detection</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test Routes</h2>
        <p>Click on any route to navigate and test session lock behavior:</p>
        
        <div class="route-grid">
            <!-- Public Routes -->
            <div class="route-card public" onclick="navigateToRoute('/')">
                <h3>🏠 Home Page</h3>
                <p><strong>Route:</strong> /</p>
                <p><strong>Type:</strong> Public</p>
                <p><strong>Auth Required:</strong> No</p>
                <p class="status">Session Lock: HIDDEN</p>
            </div>

            <div class="route-card public" onclick="navigateToRoute('/about')">
                <h3>ℹ️ About Page</h3>
                <p><strong>Route:</strong> /about</p>
                <p><strong>Type:</strong> Public</p>
                <p><strong>Auth Required:</strong> No</p>
                <p class="status">Session Lock: HIDDEN</p>
            </div>

            <div class="route-card public" onclick="navigateToRoute('/services')">
                <h3>🔧 Services Page</h3>
                <p><strong>Route:</strong> /services</p>
                <p><strong>Type:</strong> Public</p>
                <p><strong>Auth Required:</strong> No</p>
                <p class="status">Session Lock: HIDDEN</p>
            </div>

            <div class="route-card public" onclick="navigateToRoute('/contact')">
                <h3>📞 Contact Page</h3>
                <p><strong>Route:</strong> /contact</p>
                <p><strong>Type:</strong> Public</p>
                <p><strong>Auth Required:</strong> No</p>
                <p class="status">Session Lock: HIDDEN</p>
            </div>

            <div class="route-card public" onclick="navigateToRoute('/login')">
                <h3>🔑 Login Page</h3>
                <p><strong>Route:</strong> /login</p>
                <p><strong>Type:</strong> Public</p>
                <p><strong>Auth Required:</strong> No</p>
                <p class="status">Session Lock: HIDDEN</p>
            </div>

            <div class="route-card public" onclick="navigateToRoute('/socket-test')">
                <h3>🔌 Socket Test</h3>
                <p><strong>Route:</strong> /socket-test</p>
                <p><strong>Type:</strong> Public Test</p>
                <p><strong>Auth Required:</strong> No</p>
                <p class="status">Session Lock: HIDDEN</p>
            </div>

            <!-- Protected Routes -->
            <div class="route-card protected" onclick="navigateToRoute('/dashboard')">
                <h3>📊 Dashboard</h3>
                <p><strong>Route:</strong> /dashboard</p>
                <p><strong>Type:</strong> Protected</p>
                <p><strong>Auth Required:</strong> Yes</p>
                <p class="status">Session Lock: SHOWN</p>
            </div>

            <div class="route-card protected" onclick="navigateToRoute('/profile')">
                <h3>👤 Profile</h3>
                <p><strong>Route:</strong> /profile</p>
                <p><strong>Type:</strong> Protected</p>
                <p><strong>Auth Required:</strong> Yes</p>
                <p class="status">Session Lock: SHOWN</p>
            </div>

            <div class="route-card protected" onclick="navigateToRoute('/admin')">
                <h3>⚙️ Admin Dashboard</h3>
                <p><strong>Route:</strong> /admin</p>
                <p><strong>Type:</strong> Protected</p>
                <p><strong>Auth Required:</strong> Yes (Admin)</p>
                <p class="status">Session Lock: SHOWN</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Actions</h2>
        <button onclick="simulateSessionLock()">🔒 Simulate Session Lock</button>
        <button onclick="simulateSessionUnlock()" class="success">🔓 Simulate Session Unlock</button>
        <button onclick="simulateLogin()" class="warning">🔑 Simulate Login</button>
        <button onclick="simulateLogout()" class="danger">🚪 Simulate Logout</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Current Status</h2>
        <p><strong>Current Route:</strong> <span id="currentRoute">-</span></p>
        <p><strong>Session Locked:</strong> <span id="sessionLocked">-</span></p>
        <p><strong>User Authenticated:</strong> <span id="userAuthenticated">-</span></p>
        <p><strong>Should Show Lock Modal:</strong> <span id="shouldShowLock">-</span></p>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Mock state for testing
        let mockState = {
            isLocked: false,
            isAuthenticated: false,
            currentRoute: window.location.pathname
        };

        // Logging function
        function log(level, message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Update status display
        function updateStatus() {
            document.getElementById('currentRoute').textContent = mockState.currentRoute;
            document.getElementById('sessionLocked').textContent = mockState.isLocked ? 'Yes' : 'No';
            document.getElementById('userAuthenticated').textContent = mockState.isAuthenticated ? 'Yes' : 'No';
            
            const shouldShow = shouldShowSessionLock();
            document.getElementById('shouldShowLock').textContent = shouldShow ? 'Yes' : 'No';
            document.getElementById('shouldShowLock').style.color = shouldShow ? '#dc3545' : '#28a745';
        }

        // Simulate session lock logic
        function shouldShowSessionLock() {
            if (!mockState.isLocked) return false;
            if (!mockState.isAuthenticated) return false;

            const publicPages = ['/', '/about', '/services', '/contact', '/login', '/register', '/admin/login', '/theme-demo', '/socket-test'];
            const isPublicPage = publicPages.includes(mockState.currentRoute);
            
            const isProtectedPage = mockState.currentRoute.startsWith('/dashboard') ||
                                  mockState.currentRoute.startsWith('/profile') ||
                                  (mockState.currentRoute.startsWith('/admin') && mockState.currentRoute !== '/admin/login');

            return !isPublicPage && isProtectedPage;
        }

        // Navigation function
        function navigateToRoute(route) {
            mockState.currentRoute = route;
            log('info', `🧭 Navigated to: ${route}`);
            
            const shouldShow = shouldShowSessionLock();
            if (mockState.isLocked) {
                if (shouldShow) {
                    log('error', `🔒 Session lock modal SHOWN on protected page: ${route}`);
                } else {
                    log('success', `🔒 Session lock modal HIDDEN on public page: ${route}`);
                }
            }
            
            updateStatus();
        }

        // Test functions
        function simulateSessionLock() {
            mockState.isLocked = true;
            log('warning', '🔒 Session locked');
            
            const shouldShow = shouldShowSessionLock();
            if (shouldShow) {
                log('error', `🔒 Session lock modal would be SHOWN on current page: ${mockState.currentRoute}`);
            } else {
                log('success', `🔒 Session lock modal HIDDEN on public page: ${mockState.currentRoute}`);
            }
            
            updateStatus();
        }

        function simulateSessionUnlock() {
            mockState.isLocked = false;
            log('success', '🔓 Session unlocked');
            updateStatus();
        }

        function simulateLogin() {
            mockState.isAuthenticated = true;
            log('success', '🔑 User logged in');
            updateStatus();
        }

        function simulateLogout() {
            mockState.isAuthenticated = false;
            mockState.isLocked = false;
            log('info', '🚪 User logged out');
            updateStatus();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('info', 'Test log cleared');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('info', 'Session Lock Route Test initialized');
            updateStatus();
        });
    </script>
</body>
</html>
