<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Push Notifications Test - HLenergy</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #12816c, #5cad64);
            border-radius: 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            color: white;
            font-size: 24px;
        }
        
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
            font-weight: 500;
        }
        
        .status.success { background: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
        .status.error { background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .status.warning { background: #fefce8; color: #ca8a04; border: 1px solid #fef08a; }
        .status.info { background: #eff6ff; color: #2563eb; border: 1px solid #bfdbfe; }
        
        .btn {
            background: linear-gradient(135deg, #12816c, #5cad64);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 8px 8px 8px 0;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(18, 129, 108, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn.secondary {
            background: linear-gradient(135deg, #6b7280, #9ca3af);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #dc2626, #ef4444);
        }
        
        .form-group {
            margin: 16px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #12816c;
            box-shadow: 0 0 0 3px rgba(18, 129, 108, 0.1);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .indicator.green { background: #10b981; }
        .indicator.red { background: #ef4444; }
        .indicator.yellow { background: #f59e0b; }
        .indicator.gray { background: #6b7280; }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">⚡</div>
        <h1>HLenergy Push Notifications Test</h1>
        <p>Test and demonstrate push notification functionality</p>
    </div>

    <div class="card">
        <h2>🔔 Notification Status</h2>
        <div id="status-display">
            <div class="status info">
                <span class="indicator gray"></span>
                Checking browser support and permissions...
            </div>
        </div>
    </div>

    <div class="grid">
        <div class="card">
            <h3>📱 Permission & Subscription</h3>
            <button id="request-permission" class="btn" disabled>Request Permission</button>
            <button id="subscribe" class="btn" disabled>Subscribe to Notifications</button>
            <button id="unsubscribe" class="btn secondary" disabled>Unsubscribe</button>
        </div>

        <div class="card">
            <h3>🧪 Test Notifications</h3>
            <button id="test-local" class="btn">Test Local Notification</button>
            <button id="test-server" class="btn">Test Server Notification</button>
            <button id="test-lead" class="btn">Simulate New Lead</button>
        </div>
    </div>

    <div class="card">
        <h3>📝 Custom Notification</h3>
        <div class="form-group">
            <label for="custom-title">Title:</label>
            <input type="text" id="custom-title" placeholder="Custom notification title" value="HLenergy Custom Alert">
        </div>
        <div class="form-group">
            <label for="custom-message">Message:</label>
            <textarea id="custom-message" rows="3" placeholder="Custom notification message">This is a custom notification from HLenergy! 🔋</textarea>
        </div>
        <button id="send-custom" class="btn">Send Custom Notification</button>
    </div>

    <div class="card">
        <h3>📊 Activity Log</h3>
        <button id="clear-log" class="btn secondary">Clear Log</button>
        <div id="log" class="log">Initializing push notification test...\n</div>
    </div>

    <script>
        // Logging utility
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // UI state management
        const elements = {
            status: document.getElementById('status-display'),
            requestPermission: document.getElementById('request-permission'),
            subscribe: document.getElementById('subscribe'),
            unsubscribe: document.getElementById('unsubscribe'),
            testLocal: document.getElementById('test-local'),
            testServer: document.getElementById('test-server'),
            testLead: document.getElementById('test-lead'),
            sendCustom: document.getElementById('send-custom'),
            customTitle: document.getElementById('custom-title'),
            customMessage: document.getElementById('custom-message'),
            clearLog: document.getElementById('clear-log')
        };

        let registration = null;
        let subscription = null;

        // Check browser support
        function checkSupport() {
            const supported = 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
            
            if (supported) {
                log('Browser supports push notifications', 'success');
                updateStatus('Browser supports push notifications', 'success', 'green');
                elements.requestPermission.disabled = false;
            } else {
                log('Browser does not support push notifications', 'error');
                updateStatus('Browser does not support push notifications', 'error', 'red');
            }
            
            return supported;
        }

        // Update status display
        function updateStatus(message, type, indicator) {
            elements.status.innerHTML = `
                <div class="status ${type}">
                    <span class="indicator ${indicator}"></span>
                    ${message}
                </div>
            `;
        }

        // Request notification permission
        async function requestPermission() {
            try {
                const permission = await Notification.requestPermission();
                log(`Permission result: ${permission}`, permission === 'granted' ? 'success' : 'warning');
                
                if (permission === 'granted') {
                    updateStatus('Notification permission granted', 'success', 'green');
                    elements.subscribe.disabled = false;
                    await initializeServiceWorker();
                } else {
                    updateStatus('Notification permission denied', 'error', 'red');
                }
            } catch (error) {
                log(`Permission request failed: ${error.message}`, 'error');
                updateStatus('Permission request failed', 'error', 'red');
            }
        }

        // Initialize service worker
        async function initializeServiceWorker() {
            try {
                registration = await navigator.serviceWorker.register('/sw.js');
                log('Service worker registered successfully', 'success');
                
                // Wait for service worker to be ready
                await navigator.serviceWorker.ready;
                log('Service worker is ready', 'success');
                
                // Check for existing subscription
                subscription = await registration.pushManager.getSubscription();
                if (subscription) {
                    log('Found existing push subscription', 'success');
                    updateStatus('Already subscribed to push notifications', 'success', 'green');
                    elements.subscribe.disabled = true;
                    elements.unsubscribe.disabled = false;
                    elements.testServer.disabled = false;
                }
            } catch (error) {
                log(`Service worker registration failed: ${error.message}`, 'error');
            }
        }

        // Subscribe to push notifications
        async function subscribe() {
            try {
                const vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuOmLWjMpS_7VnYkYdYWjAlstT7p_4TCSK5JjdG4_f0QVo';
                
                subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
                });
                
                log('Successfully subscribed to push notifications', 'success');
                updateStatus('Subscribed to push notifications', 'success', 'green');
                
                elements.subscribe.disabled = true;
                elements.unsubscribe.disabled = false;
                elements.testServer.disabled = false;
                
                // Send subscription to server (optional for this demo)
                log('Subscription endpoint: ' + subscription.endpoint.substring(0, 50) + '...', 'info');
                
            } catch (error) {
                log(`Subscription failed: ${error.message}`, 'error');
                updateStatus('Subscription failed', 'error', 'red');
            }
        }

        // Unsubscribe from push notifications
        async function unsubscribe() {
            try {
                if (subscription) {
                    await subscription.unsubscribe();
                    subscription = null;
                    log('Successfully unsubscribed from push notifications', 'success');
                    updateStatus('Unsubscribed from push notifications', 'warning', 'yellow');
                    
                    elements.subscribe.disabled = false;
                    elements.unsubscribe.disabled = true;
                    elements.testServer.disabled = true;
                }
            } catch (error) {
                log(`Unsubscribe failed: ${error.message}`, 'error');
            }
        }

        // Test local notification
        async function testLocalNotification() {
            try {
                await registration.showNotification('HLenergy Test Notification', {
                    body: 'This is a local test notification! 🔋',
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png',
                    tag: 'test-local',
                    requireInteraction: true,
                    actions: [
                        { action: 'view', title: 'View Dashboard' },
                        { action: 'dismiss', title: 'Dismiss' }
                    ]
                });
                log('Local test notification sent', 'success');
            } catch (error) {
                log(`Local notification failed: ${error.message}`, 'error');
            }
        }

        // Test server notification (requires authentication)
        async function testServerNotification() {
            try {
                // This would require authentication in a real app
                log('Server notification test would require authentication', 'warning');
                log('Use the dashboard notifications tab for authenticated testing', 'info');
            } catch (error) {
                log(`Server notification failed: ${error.message}`, 'error');
            }
        }

        // Simulate new lead notification
        async function simulateNewLead() {
            try {
                await registration.showNotification('New Lead - HLenergy', {
                    body: 'New lead from Maria Silva: Energy Audit Inquiry',
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png',
                    tag: 'new-lead-demo',
                    requireInteraction: true,
                    actions: [
                        { action: 'view', title: 'View Lead' },
                        { action: 'dismiss', title: 'Dismiss' }
                    ],
                    data: {
                        type: 'new-lead',
                        leadId: 'demo-123'
                    }
                });
                log('New lead notification simulated', 'success');
            } catch (error) {
                log(`Lead notification failed: ${error.message}`, 'error');
            }
        }

        // Send custom notification
        async function sendCustomNotification() {
            try {
                const title = elements.customTitle.value || 'HLenergy Custom Alert';
                const message = elements.customMessage.value || 'This is a custom notification!';
                
                await registration.showNotification(title, {
                    body: message,
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png',
                    tag: 'custom-notification',
                    requireInteraction: true,
                    actions: [
                        { action: 'view', title: 'View' },
                        { action: 'dismiss', title: 'Dismiss' }
                    ]
                });
                log(`Custom notification sent: "${title}"`, 'success');
            } catch (error) {
                log(`Custom notification failed: ${error.message}`, 'error');
            }
        }

        // Utility function to convert VAPID key
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/-/g, '+')
                .replace(/_/g, '/');

            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);

            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }

        // Event listeners
        elements.requestPermission.addEventListener('click', requestPermission);
        elements.subscribe.addEventListener('click', subscribe);
        elements.unsubscribe.addEventListener('click', unsubscribe);
        elements.testLocal.addEventListener('click', testLocalNotification);
        elements.testServer.addEventListener('click', testServerNotification);
        elements.testLead.addEventListener('click', simulateNewLead);
        elements.sendCustom.addEventListener('click', sendCustomNotification);
        elements.clearLog.addEventListener('click', () => {
            document.getElementById('log').textContent = 'Log cleared.\n';
        });

        // Initialize
        if (checkSupport()) {
            // Check current permission status
            if (Notification.permission === 'granted') {
                log('Notification permission already granted', 'success');
                updateStatus('Notification permission already granted', 'success', 'green');
                elements.subscribe.disabled = false;
                initializeServiceWorker();
            } else if (Notification.permission === 'denied') {
                log('Notification permission denied', 'warning');
                updateStatus('Notification permission denied - please enable in browser settings', 'error', 'red');
            } else {
                log('Notification permission not requested yet', 'info');
                updateStatus('Click "Request Permission" to enable notifications', 'info', 'yellow');
            }
        }

        log('Push notification test page loaded', 'success');
    </script>
</body>
</html>
