<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Lock Behavior Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.locked { background: #f8d7da; color: #721c24; }
        .status.unlocked { background: #d4edda; color: #155724; }
        .status.unknown { background: #e2e3e5; color: #383d41; }
    </style>
</head>
<body>
    <h1>Session Lock Behavior Test</h1>
    <p>This test verifies that session lock modal appears on any page without redirecting to login pages.</p>

    <div class="test-section">
        <h2>Current Status</h2>
        <p>
            <strong>Current Page:</strong> <span id="currentPage">-</span>
        </p>
        <p>
            <strong>Session Status:</strong> 
            <span id="sessionStatus" class="status unknown">Unknown</span>
        </p>
        <p>
            <strong>Auth Status:</strong> 
            <span id="authStatus" class="status unknown">Unknown</span>
        </p>
        <p>
            <strong>Modal Visible:</strong> 
            <span id="modalStatus" class="status unknown">Unknown</span>
        </p>
    </div>

    <div class="test-section">
        <h2>Test Actions</h2>
        <button onclick="checkCurrentState()">Check Current State</button>
        <button onclick="simulateLockSession()" class="warning">Simulate Lock Session</button>
        <button onclick="simulateUnlockSession()" class="success">Simulate Unlock Session</button>
        <button onclick="testPageNavigation()">Test Page Navigation</button>
        <button onclick="clearLog()" class="danger">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Test Scenarios</h2>
        <button onclick="testLockOnHomePage()">Test: Lock on Home Page</button>
        <button onclick="testLockOnDashboard()">Test: Lock on Dashboard</button>
        <button onclick="testLockOnLoginPage()">Test: Lock on Login Page</button>
        <button onclick="testNavigationWhileLocked()">Test: Navigation While Locked</button>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Logging function
        function log(level, message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Status update functions
        function updateCurrentPage() {
            document.getElementById('currentPage').textContent = window.location.pathname;
        }

        function updateSessionStatus(isLocked) {
            const statusEl = document.getElementById('sessionStatus');
            if (isLocked === true) {
                statusEl.textContent = 'Locked';
                statusEl.className = 'status locked';
            } else if (isLocked === false) {
                statusEl.textContent = 'Unlocked';
                statusEl.className = 'status unlocked';
            } else {
                statusEl.textContent = 'Unknown';
                statusEl.className = 'status unknown';
            }
        }

        function updateAuthStatus(isAuthenticated) {
            const statusEl = document.getElementById('authStatus');
            if (isAuthenticated === true) {
                statusEl.textContent = 'Authenticated';
                statusEl.className = 'status unlocked';
            } else if (isAuthenticated === false) {
                statusEl.textContent = 'Not Authenticated';
                statusEl.className = 'status locked';
            } else {
                statusEl.textContent = 'Unknown';
                statusEl.className = 'status unknown';
            }
        }

        function updateModalStatus(isVisible) {
            const statusEl = document.getElementById('modalStatus');
            if (isVisible === true) {
                statusEl.textContent = 'Visible';
                statusEl.className = 'status locked';
            } else if (isVisible === false) {
                statusEl.textContent = 'Hidden';
                statusEl.className = 'status unlocked';
            } else {
                statusEl.textContent = 'Unknown';
                statusEl.className = 'status unknown';
            }
        }

        // Test functions
        function checkCurrentState() {
            log('info', 'Checking current application state...');
            updateCurrentPage();
            
            // Try to access Vue app state if available
            try {
                // This would work if we're in the Vue app context
                if (window.Vue && window.Vue.config) {
                    log('info', 'Vue app detected');
                } else {
                    log('warning', 'Vue app not detected - running in standalone mode');
                }
                
                // Check for session lock modal in DOM
                const modal = document.querySelector('[class*="session-lock"], [class*="SessionLock"]');
                updateModalStatus(!!modal);
                
                if (modal) {
                    log('success', 'Session lock modal found in DOM');
                } else {
                    log('info', 'No session lock modal found in DOM');
                }
                
            } catch (error) {
                log('error', `Error checking state: ${error.message}`);
            }
        }

        function simulateLockSession() {
            log('warning', 'Simulating session lock...');
            // This would trigger the session lock in the actual app
            log('info', 'In real app: authStore.lockSession() would be called');
            log('info', 'Expected: Modal should appear on current page without redirect');
        }

        function simulateUnlockSession() {
            log('success', 'Simulating session unlock...');
            log('info', 'In real app: authStore.unlockSession() would be called');
            log('info', 'Expected: Modal should disappear, user stays on current page');
        }

        function testPageNavigation() {
            log('info', 'Testing page navigation...');
            const currentPath = window.location.pathname;
            log('info', `Current path: ${currentPath}`);
            log('info', 'In real app: router.push() would be tested');
            log('info', 'Expected: Navigation should work normally when unlocked');
        }

        function testLockOnHomePage() {
            log('info', '=== Testing Lock on Home Page ===');
            log('info', '1. Navigate to home page');
            log('info', '2. Lock session');
            log('info', '3. Verify modal appears without redirect');
            log('info', 'Expected: User stays on home page, modal shows');
        }

        function testLockOnDashboard() {
            log('info', '=== Testing Lock on Dashboard ===');
            log('info', '1. Navigate to dashboard');
            log('info', '2. Lock session');
            log('info', '3. Verify modal appears without redirect');
            log('info', 'Expected: User stays on dashboard, modal shows');
        }

        function testLockOnLoginPage() {
            log('info', '=== Testing Lock on Login Page ===');
            log('info', '1. Navigate to login page');
            log('info', '2. Lock session');
            log('info', '3. Verify modal appears (previously it was hidden on login pages)');
            log('info', 'Expected: Modal shows even on login page');
        }

        function testNavigationWhileLocked() {
            log('info', '=== Testing Navigation While Locked ===');
            log('info', '1. Lock session');
            log('info', '2. Attempt to navigate to different pages');
            log('info', '3. Verify modal persists and no redirects occur');
            log('info', 'Expected: Modal blocks interaction, no redirects to login');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('info', 'Log cleared');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('info', 'Session Lock Behavior Test initialized');
            checkCurrentState();
            
            // Set up periodic state checking
            setInterval(checkCurrentState, 5000);
        });

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                log('info', 'Page became visible - checking state');
                checkCurrentState();
            }
        });
    </script>
</body>
</html>
