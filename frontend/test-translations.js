// Quick test to verify Portuguese translations are working
import { createI18n } from 'vue-i18n'
import pt from './src/locales/pt.json'

const i18n = createI18n({
  legacy: false,
  locale: 'pt',
  fallbackLocale: 'en',
  messages: {
    pt
  }
})

// Test some key translations
console.log('Testing Portuguese translations:')
console.log('Dashboard welcome:', pt.dashboard.welcome)
console.log('Leads status new:', pt.leads.status.new)
console.log('Leads status contacted:', pt.leads.status.contacted)
console.log('Leads status qualified:', pt.leads.status.qualified)
console.log('Leads actions view:', pt.leads.actions.view)
console.log('Dashboard tabs overview:', pt.dashboard.tabs.overview)
console.log('Dashboard tabs leads:', pt.dashboard.tabs.leads)

console.log('\nAll translations loaded successfully!')
