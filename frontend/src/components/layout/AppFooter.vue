<template>
  <footer class="footer footer-center bg-base-200 text-base-content p-10">
    <!-- Logo and Company Info -->
    <div class="flex flex-col items-center mb-6">
      <Logo size="lg" :show-text="true" class="mb-4" />
      <p class="text-base-content/70 max-w-md text-center">
        Professional energy consultation services for businesses and homeowners.
        Optimizing energy consumption and driving sustainable solutions.
      </p>
    </div>

    <!-- Navigation Links -->
    <nav class="grid grid-flow-col gap-4">
      <RouterLink to="/about" class="link link-hover">{{ $t('navigation.about') }}</RouterLink>
      <RouterLink to="/services" class="link link-hover">{{ $t('navigation.services') }}</RouterLink>
      <RouterLink to="/contact" class="link link-hover">{{ $t('navigation.contact') }}</RouterLink>
      <RouterLink to="/privacy-policy" class="link link-hover">Privacy Policy</RouterLink>
    </nav>

    <!-- Privacy & Consent Link -->
    <nav class="text-sm">
      <button
        @click="showConsentSettings"
        class="link link-hover text-base-content/70 hover:text-base-content"
      >
        <Icon name="shield-check" size="xs" class="mr-1" />
        Privacy & Cookie Settings
      </button>
    </nav>
    
    <!-- Social Media Links -->
    <nav>
      <div class="grid grid-flow-col gap-4">
        <!-- Facebook -->
        <a
          href="https://facebook.com/share/1ArWY9phE5/?mibextid=wwxifr"
          target="_blank"
          rel="noopener noreferrer"
          class="link link-hover hover:text-primary transition-colors duration-200"
          :title="$t('social.follow_facebook')"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="fill-current hover:scale-110 transition-transform duration-200"
          >
            <path
              d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"
            ></path>
          </svg>
        </a>

        <!-- Instagram -->
        <a
          href="https://www.instagram.com/hl_energy_"
          target="_blank"
          rel="noopener noreferrer"
          class="link link-hover hover:text-secondary transition-colors duration-200"
          :title="$t('social.follow_instagram')"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="fill-current hover:scale-110 transition-transform duration-200"
          >
            <path
              d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
            ></path>
          </svg>
        </a>
      </div>
    </nav>
    
    <!-- Company Info and Copyright -->
    <aside class="text-center">
      <div class="flex flex-wrap justify-center items-center gap-4 mb-4 text-sm text-base-content/60">
        <span>ISO 50001 Certified</span>
        <span>•</span>
        <span>15+ Years Experience</span>
        <span>•</span>
        <span>500+ Satisfied Clients</span>
      </div>
      <p class="text-base-content/70">
        Copyright © {{ currentYear }} HLenergy. All rights reserved.
      </p>
    </aside>

    <!-- Consent Settings Modal -->
    <Teleport to="body">
      <div
        v-if="showConsentModal"
        class="modal modal-open"
        @click.self="closeConsentModal"
      >
        <div class="modal-box max-w-4xl">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-base-content">Privacy & Cookie Settings</h3>
            <button
              @click="closeConsentModal"
              class="btn btn-ghost btn-sm btn-circle"
            >
              <Icon name="x" size="sm" />
            </button>
          </div>

          <!-- Consent Quick Settings Component -->
          <ConsentQuickSettings />

          <div class="modal-action">
            <button @click="closeConsentModal" class="btn btn-ghost">
              Close
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </footer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useConsentStore } from '@/stores/consent'
import Logo from '@/components/common/Logo.vue'
import Icon from '@/components/common/Icon.vue'
import ConsentQuickSettings from '@/components/consent/ConsentQuickSettings.vue'

// Stores
const consentStore = useConsentStore()

// Computed
const currentYear = computed(() => new Date().getFullYear())

// State for consent settings modal
const showConsentModal = ref(false)

// Methods
const showConsentSettings = () => {
  showConsentModal.value = true
}

const closeConsentModal = () => {
  showConsentModal.value = false
}
</script>
