<template>
  <footer class="footer footer-center bg-base-200 text-base-content p-10">
    <!-- Logo and Company Info -->
    <div class="flex flex-col items-center mb-6">
      <Logo size="lg" :show-text="true" class="mb-4" />
      <p class="text-base-content/70 max-w-md text-center">
        Professional energy consultation services for businesses and homeowners.
        Optimizing energy consumption and driving sustainable solutions.
      </p>
    </div>

    <!-- Navigation Links -->
    <nav class="grid grid-flow-col gap-4">
      <RouterLink to="/about" class="link link-hover">{{ $t('navigation.about') }}</RouterLink>
      <RouterLink to="/services" class="link link-hover">{{ $t('navigation.services') }}</RouterLink>
      <RouterLink to="/contact" class="link link-hover">{{ $t('navigation.contact') }}</RouterLink>
      <RouterLink to="/privacy-policy" class="link link-hover">Privacy Policy</RouterLink>
    </nav>

    <!-- Privacy & Consent Link -->
    <nav class="text-sm">
      <button
        @click="showConsentSettings"
        class="link link-hover text-base-content/70 hover:text-base-content"
      >
        <Icon name="shield-check" size="xs" class="mr-1" />
        Privacy & Cookie Settings
      </button>
    </nav>
    
    <nav>
      <div class="grid grid-flow-col gap-4">
        <a href="#" class="link link-hover">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="fill-current"
          >
            <path
              d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"
            ></path>
          </svg>
        </a>
        <a href="#" class="link link-hover">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="fill-current"
          >
            <path
              d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"
            ></path>
          </svg>
        </a>
        <a href="#" class="link link-hover">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            class="fill-current"
          >
            <path
              d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"
            ></path>
          </svg>
        </a>
      </div>
    </nav>
    
    <!-- Company Info and Copyright -->
    <aside class="text-center">
      <div class="flex flex-wrap justify-center items-center gap-4 mb-4 text-sm text-base-content/60">
        <span>ISO 50001 Certified</span>
        <span>•</span>
        <span>15+ Years Experience</span>
        <span>•</span>
        <span>500+ Satisfied Clients</span>
      </div>
      <p class="text-base-content/70">
        Copyright © {{ currentYear }} HLenergy. All rights reserved.
      </p>
    </aside>

    <!-- Consent Settings Modal -->
    <Teleport to="body">
      <div
        v-if="showConsentModal"
        class="modal modal-open"
        @click.self="closeConsentModal"
      >
        <div class="modal-box max-w-4xl">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-base-content">Privacy & Cookie Settings</h3>
            <button
              @click="closeConsentModal"
              class="btn btn-ghost btn-sm btn-circle"
            >
              <Icon name="x" size="sm" />
            </button>
          </div>

          <!-- Consent Quick Settings Component -->
          <ConsentQuickSettings />

          <div class="modal-action">
            <button @click="closeConsentModal" class="btn btn-ghost">
              Close
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </footer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useConsentStore } from '@/stores/consent'
import Logo from '@/components/common/Logo.vue'
import Icon from '@/components/common/Icon.vue'
import ConsentQuickSettings from '@/components/consent/ConsentQuickSettings.vue'

// Stores
const consentStore = useConsentStore()

// Computed
const currentYear = computed(() => new Date().getFullYear())

// State for consent settings modal
const showConsentModal = ref(false)

// Methods
const showConsentSettings = () => {
  showConsentModal.value = true
}

const closeConsentModal = () => {
  showConsentModal.value = false
}
</script>
