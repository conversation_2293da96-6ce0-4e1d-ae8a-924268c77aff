<template>
  <div class="captcha-verification">
    <div
      class="card border shadow-sm p-4 transition-all duration-300"
      :class="{
        'bg-base-100 border-base-200/50': !hasError && !isVerified,
        'bg-error/5 border-error/30 animate-pulse': hasError,
        'bg-success/5 border-success/30': isVerified,
        'bg-warning/5 border-warning/30': isVerifying
      }"
    >
      <div class="flex items-center gap-3">
        <div class="form-control">
          <label class="label cursor-pointer gap-3">
            <input
              type="checkbox"
              class="checkbox checkbox-primary"
              v-model="isChecked"
              @change="handleVerification"
              :disabled="isVerifying"
              :class="{
                'checkbox-error animate-pulse': hasError,
                'checkbox-success': isVerified
              }"
            />
            <span
              class="label-text font-medium"
              :class="{
                'text-error': hasError,
                'text-success': isVerified,
                'text-warning': isVerifying
              }"
            >
              {{ $t('contact.captcha.verify_human') }}
            </span>
          </label>
        </div>
        
        <!-- Loading state -->
        <div v-if="isVerifying" class="loading loading-spinner loading-sm text-primary"></div>
        
        <!-- Success state -->
        <div v-else-if="isVerified" class="flex items-center gap-1 text-success">
          <Icon name="check-circle" size="sm" />
          <span class="text-sm font-medium">{{ $t('contact.captcha.verified') }}</span>
        </div>
        
        <!-- Error state -->
        <div v-else-if="hasError" class="flex items-center gap-1 text-error">
          <Icon name="x-circle" size="sm" />
          <span class="text-sm">{{ $t('contact.captcha.error') }}</span>
        </div>
      </div>

      <!-- Warning message for incomplete CAPTCHA -->
      <div v-if="showWarning" class="mt-3 p-3 bg-warning/10 border border-warning/20 rounded-lg animate-fade-in">
        <div class="flex items-center gap-2">
          <Icon name="alert-triangle" size="sm" class="text-warning" />
          <span class="text-sm font-medium text-warning">{{ $t('contact.captcha.warning') }}</span>
        </div>
        <p class="text-xs text-base-content/70 mt-1">{{ $t('contact.captcha.warning_message') }}</p>
      </div>
      
      <!-- Simple puzzle for additional verification -->
      <div v-if="showPuzzle" class="mt-4 p-3 bg-base-200/50 rounded-lg">
        <div class="flex items-center gap-3">
          <span class="text-sm font-medium">{{ puzzleQuestion }}</span>
          <input
            type="text"
            v-model="puzzleAnswer"
            @input="checkPuzzleAnswer"
            class="input input-bordered input-sm w-24"
            :placeholder="$t('contact.captcha.answer')"
            maxlength="10"
          />
          <div v-if="puzzleCorrect" class="text-success">
            <Icon name="check" size="sm" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()

// Props
interface Props {
  modelValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'verified': [token: string]
  'error': [error: string]
}>()

// State
const isChecked = ref(false)
const isVerifying = ref(false)
const isVerified = ref(false)
const hasError = ref(false)
const showPuzzle = ref(false)
const puzzleAnswer = ref('')
const puzzleCorrect = ref(false)
const showWarning = ref(false)

// Simple puzzles
const puzzles = [
  { question: 'What color is the sky?', answer: 'blue' },
  { question: 'How many days in a week?', answer: '7' },
  { question: 'What animal says "meow"?', answer: 'cat' },
  { question: 'What do bees make?', answer: 'honey' },
  { question: 'How many legs does a spider have?', answer: '8' },
  { question: 'What color is grass?', answer: 'green' },
  { question: 'What do we use to see?', answer: 'eyes' },
  { question: 'How many fingers on one hand?', answer: '5' }
]

const currentPuzzle = ref(puzzles[0])
const puzzleQuestion = computed(() => currentPuzzle.value.question)
const correctPuzzleAnswer = computed(() => currentPuzzle.value.answer.toLowerCase())

// Methods
const generatePuzzle = () => {
  const randomIndex = Math.floor(Math.random() * puzzles.length)
  currentPuzzle.value = puzzles[randomIndex]
  puzzleAnswer.value = ''
  puzzleCorrect.value = false
}

const checkPuzzleAnswer = () => {
  const userAnswer = puzzleAnswer.value.toLowerCase().trim()
  if (userAnswer === correctPuzzleAnswer.value) {
    puzzleCorrect.value = true
    completeVerification()
  } else {
    puzzleCorrect.value = false
  }
}

const handleVerification = async () => {
  if (!isChecked.value) {
    reset()
    return
  }

  isVerifying.value = true
  hasError.value = false

  try {
    // Simulate verification delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show simple puzzle for additional security
    showPuzzle.value = true
    generatePuzzle()
    isVerifying.value = false

  } catch (error) {
    hasError.value = true
    isVerifying.value = false
    isChecked.value = false
    emit('error', 'Verification failed')
  }
}

const completeVerification = () => {
  isVerified.value = true
  showPuzzle.value = false

  // Generate a simple verification token
  const token = `captcha_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  emit('update:modelValue', true)
  emit('verified', token)
}

const reset = () => {
  isVerified.value = false
  hasError.value = false
  showPuzzle.value = false
  puzzleCorrect.value = false
  puzzleAnswer.value = ''
  showWarning.value = false
  emit('update:modelValue', false)
}

const showCaptchaWarning = () => {
  showWarning.value = true
  setTimeout(() => {
    showWarning.value = false
  }, 5000) // Hide warning after 5 seconds
}

// Watch for external changes
const updateFromParent = (value: boolean) => {
  if (!value) {
    reset()
    isChecked.value = false
  }
}

// Initialize
onMounted(() => {
  generatePuzzle()
})

// Expose methods for parent component
defineExpose({
  reset,
  showCaptchaWarning,
  isVerified: computed(() => isVerified.value)
})
</script>

<style scoped>
.captcha-verification {
  user-select: none;
}

.checkbox:checked {
  animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
  0% { transform: scale(0.8); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
