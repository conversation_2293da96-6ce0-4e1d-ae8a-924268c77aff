<template>
  <div v-if="shouldShowInstallPrompt" class="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
    <div class="card bg-gradient-to-br from-info/10 to-info/5 border border-info/20 shadow-2xl backdrop-blur-sm animate-fade-in-up">
      <div class="card-body p-4">
        <div class="flex items-start gap-3">
          <div class="p-2 bg-info/20 rounded-lg flex-shrink-0">
            <Icon name="smartphone" size="sm" class="text-info" />
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="font-bold text-info mb-1">Install HLenergy App</h3>
            <div class="text-xs text-base-content/70">Get the full app experience with offline access</div>
          </div>
          <div class="flex gap-2 flex-shrink-0">
            <button @click="handleInstall" class="btn btn-sm btn-info hover:btn-info-focus transition-all duration-200">
              Install
            </button>
            <button @click="handleDismiss" class="btn btn-sm btn-ghost hover:btn-error transition-all duration-200">
              ✕
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePWA } from '@/composables/usePWA'
import Icon from '@/components/common/Icon.vue'

const { shouldShowInstallPrompt, handleInstallPrompt, dismissInstallPrompt } = usePWA()

const handleInstall = async () => {
  const installed = await handleInstallPrompt()
  if (installed) {
    // Show success message or redirect
    console.log('App installed successfully!')
  }
}

const handleDismiss = () => {
  dismissInstallPrompt()
}
</script>
