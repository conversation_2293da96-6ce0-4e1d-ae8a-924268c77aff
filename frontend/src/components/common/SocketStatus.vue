<template>
  <div class="socket-status">
    <!-- Compact Status Indicator -->
    <div
      v-if="!expanded"
      class="flex items-center space-x-2 cursor-pointer hover:bg-base-200 rounded-lg px-2 py-1 transition-colors"
      @click="openPopup"
      role="button"
      tabindex="0"
      @keydown.enter="openPopup"
      @keydown.space.prevent="openPopup"
      aria-label="Open Socket.io status panel"
    >
      <div 
        class="w-2 h-2 rounded-full transition-colors duration-300"
        :class="{
          'bg-success animate-pulse': status.isConnected,
          'bg-warning animate-pulse': status.isConnecting,
          'bg-orange-500 animate-pulse': status.status === 'backoff',
          'bg-error': status.status === 'error',
          'bg-neutral': status.status === 'disconnected'
        }"
      ></div>
      <span class="text-xs text-base-content/70 font-medium">{{ status.getStatusText() }}</span>
    </div>

    <!-- Status Popup Modal -->
    <Teleport to="body">
      <div
        v-if="expanded"
        class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
        @click.self="closePopup"
        @keydown.esc="closePopup"
        tabindex="-1"
      >
        <div
          class="bg-base-100 rounded-xl p-6 shadow-2xl border border-base-300 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto transform transition-all duration-200 scale-100 relative"
          @click.stop
          role="dialog"
          aria-modal="true"
          aria-labelledby="socket-status-title"
        >
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-4">
          <h3 id="socket-status-title" class="text-lg font-semibold flex items-center">
            <Icon :name="status.getStatusIcon()" size="md" class="mr-3" />
            Socket.io Status
          </h3>
          <button
            @click="closePopup"
            class="btn btn-ghost btn-sm"
            aria-label="Close status panel"
          >
            <Icon name="x" size="md" />
          </button>
        </div>

      <!-- Connection Status -->
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm">Status:</span>
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="{
                'bg-success animate-pulse': status.isConnected,
                'bg-warning animate-pulse': status.isConnecting,
                'bg-orange-500 animate-pulse': status.status === 'backoff',
                'bg-error': status.status === 'error',
                'bg-neutral': status.status === 'disconnected'
              }"
            ></div>
            <span 
              class="text-sm font-medium"
              :class="{
                'text-success': status.isConnected,
                'text-warning': status.isConnecting,
                'text-orange-500': status.status === 'backoff',
                'text-error': status.status === 'error',
                'text-neutral': status.status === 'disconnected'
              }"
            >
              {{ status.getStatusText() }}
            </span>
          </div>
        </div>

        <!-- Connection Details -->
        <div v-if="status.isConnected" class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <span>Server:</span>
            <span class="font-mono text-xs">{{ serverUrl }}</span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span>Transport:</span>
            <span class="badge badge-sm badge-primary">WebSocket</span>
          </div>
          
          <div v-if="lastPing" class="flex items-center justify-between text-sm">
            <span>Last Ping:</span>
            <span class="text-xs">{{ formatPingTime(lastPing) }}</span>
          </div>
        </div>

        <!-- Error Details -->
        <div v-if="status.error" class="space-y-2">
          <div class="text-sm text-error">
            <strong>Error:</strong> {{ status.error }}
          </div>
        </div>

        <!-- Reconnection Status -->
        <div v-if="status.reconnectAttempts > 0 || status.isInBackoffPeriod" class="space-y-2">
          <div class="text-sm">
            <strong>Reconnection Status:</strong>
          </div>

          <div v-if="status.isInBackoffPeriod" class="text-sm text-warning">
            <Icon name="clock" size="sm" class="inline mr-1" />
            Waiting {{ status.reconnectCountdown }}s before next cycle ({{ status.reconnectCycle }})
          </div>

          <div v-else-if="status.reconnectAttempts > 0" class="text-sm text-info">
            <Icon name="refresh" size="sm" class="inline mr-1" />
            Attempt {{ status.reconnectAttempts }}/{{ status.maxReconnectAttempts }}
          </div>

          <div class="text-xs text-base-content/60">
            Strategy: {{ status.maxReconnectAttempts }} attempts, then 1min+ backoff
          </div>
        </div>

        <!-- Connection Actions -->
        <div class="flex space-x-2 pt-2">
          <button 
            v-if="!status.isConnected && !status.isConnecting"
            @click="status.connect()"
            class="btn btn-primary btn-sm flex-1"
          >
            <Icon name="refresh" size="sm" class="mr-1" />
            Connect
          </button>
          
          <button 
            v-if="status.isConnected"
            @click="status.disconnect()"
            class="btn btn-error btn-sm flex-1"
          >
            <Icon name="wifi-off" size="sm" class="mr-1" />
            Disconnect
          </button>
          
          <button 
            v-if="status.isConnecting"
            disabled
            class="btn btn-warning btn-sm flex-1"
          >
            <span class="loading loading-spinner loading-sm mr-1"></span>
            Connecting...
          </button>
        </div>

        <!-- Real-time Features Status -->
        <div class="pt-3 border-t border-base-300">
          <h4 class="text-sm font-medium mb-2">Real-time Features</h4>
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div class="flex items-center space-x-1">
              <Icon 
                name="chart-line" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Analytics</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon 
                name="bell" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Notifications</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon 
                name="message-circle" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Chat</span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon 
                name="activity" 
                size="xs" 
                :class="status.isConnected ? 'text-success' : 'text-neutral'"
              />
              <span>Live Updates</span>
            </div>
          </div>
        </div>
        </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject, nextTick, onUnmounted } from 'vue'
import { getGlobalSocket } from '@/plugins/socket'
import Icon from '@/components/common/Icon.vue'

// Props
interface Props {
  autoExpand?: boolean
  showDetails?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoExpand: false,
  showDetails: true
})

// State
const expanded = ref(props.autoExpand)
const socket = getGlobalSocket() || inject('socket')

// Create status object from socket
const status = computed(() => {
  if (!socket) return {
    isConnected: false,
    isConnecting: false,
    status: 'disconnected',
    error: 'Socket not available',
    reconnectAttempts: 0,
    getStatusText: () => 'Disconnected',
    getStatusIcon: () => 'wifi-off',
    connect: () => {},
    disconnect: () => {}
  }

  return {
    isConnected: socket.isConnected?.value || false,
    isConnecting: socket.isConnecting?.value || false,
    status: socket.connectionStatus?.value || 'disconnected',
    error: socket.connectionError?.value || null,
    reconnectAttempts: socket.reconnectAttempts?.value || 0,
    reconnectCycle: socket.reconnectCycle?.value || 0,
    isInBackoffPeriod: socket.isInBackoffPeriod?.value || false,
    reconnectCountdown: socket.reconnectCountdown?.value || 0,
    maxReconnectAttempts: socket.maxReconnectAttempts?.value || 3,
    getStatusText: () => {
      if (socket.isConnected?.value) return 'Connected'
      if (socket.isConnecting?.value) return 'Connecting...'
      if (socket.isInBackoffPeriod?.value) return `Backoff (${socket.reconnectCountdown?.value || 0}s)`
      if (socket.connectionError?.value) return 'Error'
      return 'Disconnected'
    },
    getStatusIcon: () => {
      if (socket.isConnected?.value) return 'wifi'
      if (socket.isConnecting?.value) return 'loader'
      if (socket.isInBackoffPeriod?.value) return 'clock'
      if (socket.connectionError?.value) return 'wifi-off'
      return 'wifi-off'
    },
    connect: () => socket.connect?.(),
    disconnect: () => socket.disconnect?.()
  }
})

// Computed
const serverUrl = computed(() => {
  return import.meta.env.VITE_API_URL || 'http://localhost:3001'
})

const lastPing = computed(() => {
  return socket?.lastPing?.value
})

// Methods
const formatPingTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 1000) return 'Just now'
  if (diff < 60000) return `${Math.floor(diff / 1000)}s ago`
  return `${Math.floor(diff / 60000)}m ago`
}

// Methods
const openPopup = async () => {
  expanded.value = true
  await nextTick()
  // Focus the modal for keyboard accessibility
  const modal = document.querySelector('.socket-status .fixed')
  if (modal) {
    (modal as HTMLElement).focus()
  }
}

const closePopup = () => {
  expanded.value = false
}

// Auto-expand on error
watch(() => status.value.status, (newStatus) => {
  if (newStatus === 'error' && props.autoExpand) {
    openPopup()
  }
})

// Store escape handler for proper cleanup
let escapeHandler: ((e: KeyboardEvent) => void) | null = null

// Handle escape key globally when popup is open
watch(expanded, (isExpanded) => {
  if (isExpanded) {
    escapeHandler = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closePopup()
      }
    }
    document.addEventListener('keydown', escapeHandler)
  } else if (escapeHandler) {
    // Clean up when popup closes
    document.removeEventListener('keydown', escapeHandler)
    escapeHandler = null
  }
})

// CRITICAL: Clean up escape handler on unmount
onUnmounted(() => {
  if (escapeHandler) {
    document.removeEventListener('keydown', escapeHandler)
    escapeHandler = null
    console.log('✅ SocketStatus escape handler cleaned up')
  }
})
</script>

<style scoped>
.socket-status {
  position: relative;
  z-index: 50;
}

/* Smooth transitions */
.socket-status * {
  transition: all 0.2s ease-in-out;
}

/* Modal animations */
.fixed {
  animation: fadeIn 0.2s ease-out;
}

.fixed > div {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Pulse animation for connecting state */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Hover effects */
.cursor-pointer:hover {
  transform: scale(1.02);
}

/* Focus styles for accessibility */
.cursor-pointer:focus {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}
</style>
