<template>
  <div class="recaptcha-v3">
    <!-- reCAPTCHA v3 is invisible, but we show a status indicator -->
    <div
      class="card border shadow-sm p-4 transition-all duration-300"
      :class="{
        'bg-base-100 border-base-200/50': !hasError && !isVerified,
        'bg-error/5 border-error/30 animate-pulse': hasError,
        'bg-success/5 border-success/30': isVerified,
        'bg-warning/5 border-warning/30': isLoading
      }"
    >
      <div class="flex items-center gap-3">
        <!-- Status Icon -->
        <div class="flex-shrink-0">
          <div v-if="isLoading" class="loading loading-spinner loading-sm text-primary"></div>
          <Icon v-else-if="isVerified" name="check-circle" size="sm" class="text-success" />
          <Icon v-else-if="hasError" name="x-circle" size="sm" class="text-error" />
          <Icon v-else name="shield" size="sm" class="text-base-content/60" />
        </div>

        <!-- Status Text -->
        <div class="flex-1">
          <div class="flex items-center gap-2">
            <span
              class="text-sm font-medium"
              :class="{
                'text-error': hasError,
                'text-success': isVerified,
                'text-warning': isLoading,
                'text-base-content': !hasError && !isVerified && !isLoading
              }"
            >
              {{ statusText }}
            </span>
            
            <!-- Google reCAPTCHA Badge -->
            <div class="text-xs text-base-content/50">
              Protected by reCAPTCHA
            </div>
          </div>
          
          <!-- Error message -->
          <div v-if="hasError && errorMessage" class="text-xs text-error mt-1">
            {{ errorMessage }}
          </div>
        </div>

        <!-- Retry Button -->
        <div v-if="hasError" class="flex-shrink-0">
          <button
            @click="executeRecaptcha"
            class="btn btn-sm btn-outline btn-error"
            :disabled="isLoading"
          >
            <Icon name="refresh" size="sm" class="mr-1" />
            {{ $t('contact.captcha.retry', 'Retry') }}
          </button>
        </div>
      </div>
    </div>

    <!-- reCAPTCHA Terms -->
    <div class="text-xs text-base-content/50 mt-2 text-center">
      {{ $t('contact.captcha.terms_prefix', 'This site is protected by reCAPTCHA and the Google') }}
      <a href="https://policies.google.com/privacy" target="_blank" class="link link-primary">
        {{ $t('contact.captcha.privacy_policy', 'Privacy Policy') }}
      </a>
      {{ $t('contact.captcha.and', 'and') }}
      <a href="https://policies.google.com/terms" target="_blank" class="link link-primary">
        {{ $t('contact.captcha.terms_service', 'Terms of Service') }}
      </a>
      {{ $t('contact.captcha.apply', 'apply') }}.
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()

// Props
interface Props {
  modelValue?: boolean
  action?: string // reCAPTCHA action name (e.g., 'contact_form')
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  action: 'contact_form'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'verified': [token: string]
  'error': [error: string]
}>()

// State
const isLoading = ref(false)
const isVerified = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const recaptchaToken = ref('')

// Computed
const statusText = computed(() => {
  if (isLoading.value) return t('contact.captcha.verifying', 'Verifying...')
  if (isVerified.value) return t('contact.captcha.verified', 'Verified')
  if (hasError.value) return t('contact.captcha.error', 'Verification failed')
  return t('contact.captcha.ready', 'Ready for verification')
})

// reCAPTCHA configuration
const RECAPTCHA_SITE_KEY = import.meta.env.VITE_RECAPTCHA_SITE_KEY
const RECAPTCHA_SCRIPT_ID = 'recaptcha-v3-script'

// Methods
const loadRecaptchaScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (document.getElementById(RECAPTCHA_SCRIPT_ID)) {
      resolve()
      return
    }

    // Check if grecaptcha is already available
    if (window.grecaptcha) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.id = RECAPTCHA_SCRIPT_ID
    script.src = `https://www.google.com/recaptcha/api.js?render=${RECAPTCHA_SITE_KEY}`
    script.async = true
    script.defer = true

    script.onload = () => {
      // Wait for grecaptcha to be ready
      window.grecaptcha.ready(() => {
        resolve()
      })
    }

    script.onerror = () => {
      reject(new Error('Failed to load reCAPTCHA script'))
    }

    document.head.appendChild(script)
  })
}

const executeRecaptcha = async (): Promise<void> => {
  if (!RECAPTCHA_SITE_KEY) {
    console.warn('reCAPTCHA site key not configured')
    handleError('reCAPTCHA not configured')
    return
  }

  try {
    isLoading.value = true
    hasError.value = false
    errorMessage.value = ''

    // Load reCAPTCHA script if not already loaded
    await loadRecaptchaScript()

    // Execute reCAPTCHA
    const token = await window.grecaptcha.execute(RECAPTCHA_SITE_KEY, {
      action: props.action
    })

    if (token) {
      recaptchaToken.value = token
      isVerified.value = true
      emit('update:modelValue', true)
      emit('verified', token)
      console.log('reCAPTCHA verification successful')
    } else {
      throw new Error('No token received from reCAPTCHA')
    }
  } catch (error: any) {
    console.error('reCAPTCHA execution failed:', error)
    handleError(error.message || 'reCAPTCHA verification failed')
  } finally {
    isLoading.value = false
  }
}

const handleError = (message: string): void => {
  hasError.value = true
  errorMessage.value = message
  isVerified.value = false
  recaptchaToken.value = ''
  emit('update:modelValue', false)
  emit('error', message)
}

const reset = (): void => {
  isLoading.value = false
  isVerified.value = false
  hasError.value = false
  errorMessage.value = ''
  recaptchaToken.value = ''
  emit('update:modelValue', false)
}

const showCaptchaWarning = (): void => {
  if (!isVerified.value) {
    hasError.value = true
    errorMessage.value = t('contact.captcha.warning_message')
  }
}

// Expose methods for parent component
defineExpose({
  reset,
  showCaptchaWarning,
  executeRecaptcha
})

// Auto-execute reCAPTCHA when component mounts
onMounted(async () => {
  // Small delay to ensure component is fully mounted
  setTimeout(() => {
    executeRecaptcha()
  }, 500)
})

// Cleanup
onUnmounted(() => {
  // Note: We don't remove the reCAPTCHA script as it might be used by other components
})

// Global type declaration for grecaptcha
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void
      execute: (siteKey: string, options: { action: string }) => Promise<string>
    }
  }
}
</script>

<style scoped>
/* Additional styles for reCAPTCHA component */
.recaptcha-v3 {
  /* Ensure proper spacing and layout */
}
</style>
