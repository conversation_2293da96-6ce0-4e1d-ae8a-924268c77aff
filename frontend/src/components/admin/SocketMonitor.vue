<template>
  <div class="socket-monitor">
    <!-- Real-time Status Header -->
    <div class="alert mb-6" :class="isConnectedStatus ? 'alert-success' : 'alert-warning'">
      <div class="flex items-center gap-2">
        <div class="flex items-center gap-2">
          <div :class="['w-3 h-3 rounded-full', isConnectedStatus ? 'bg-success animate-pulse' : 'bg-warning']"></div>
          <span class="font-semibold">
            {{ isConnectedStatus ? 'Real-time monitoring active' : 'Real-time monitoring unavailable' }}
          </span>
        </div>
        <div v-if="lastUpdate" class="text-sm opacity-70">
          Last update: {{ lastUpdate }}
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Connection Status -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Socket.io Status</h3>
          <div class="flex items-center gap-2">
            <div :class="['badge', isConnected ? 'badge-success' : 'badge-error']">
              {{ isConnected ? 'Connected' : 'Disconnected' }}
            </div>
            <span class="text-xs opacity-70">{{ connectionStatus }}</span>
          </div>
        </div>
      </div>

      <!-- Active Connections -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Active Connections</h3>
          <div class="text-2xl font-bold">{{ metrics.activeConnections || 0 }}</div>
          <div class="text-xs opacity-70">Peak: {{ metrics.peakConnections || 0 }}</div>
        </div>
      </div>

      <!-- Total Events -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Total Events</h3>
          <div class="text-2xl font-bold">{{ metrics.totalEvents || 0 }}</div>
          <div class="text-xs opacity-70">{{ eventsPerSecond }} events/sec</div>
        </div>
      </div>

      <!-- Response Time -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Avg Response Time</h3>
          <div class="text-2xl font-bold">{{ responseTime }}ms</div>
          <div class="text-xs opacity-70">Last updated: {{ lastUpdate }}</div>
        </div>
      </div>
    </div>

    <!-- Memory Monitoring -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- Memory Usage -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Memory Usage</h3>
          <div class="text-2xl font-bold">{{ memoryUsage }}MB</div>
          <div class="text-xs opacity-70">Heap: {{ heapUsage }}MB</div>
        </div>
      </div>

      <!-- Memory Health -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Memory Health</h3>
          <div class="text-2xl font-bold" :class="memoryHealthClass">{{ memoryHealth }}</div>
          <div class="text-xs opacity-70">{{ memoryWarnings }} warnings</div>
        </div>
      </div>

      <!-- Memory Actions -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-sm">Memory Actions</h3>
          <button @click="forceCleanup" class="btn btn-sm btn-outline btn-warning w-full mb-2">
            Force Cleanup
          </button>
          <button @click="fetchMemoryStats" class="btn btn-sm btn-outline btn-info w-full">
            Refresh Memory
          </button>
        </div>
      </div>
    </div>

    <!-- Online Users -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <div class="flex justify-between items-center mb-4">
          <h3 class="card-title">Online Users ({{ onlineUsers.length }})</h3>
          <button @click="refreshUsers" class="btn btn-sm btn-outline">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh
          </button>
        </div>
        
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>User</th>
                <th>Role</th>
                <th>Type</th>
                <th>Socket ID</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in onlineUsers" :key="user.socketId">
                <td>
                  <div class="flex items-center gap-2">
                    <div class="avatar placeholder">
                      <div class="bg-neutral-focus text-neutral-content rounded-full w-8">
                        <span class="text-xs">{{ user.userData.name?.charAt(0) || 'U' }}</span>
                      </div>
                    </div>
                    <div>
                      <div class="font-bold text-sm">{{ user.userData.name || 'Unknown' }}</div>
                      <div class="text-xs opacity-70">{{ user.userData.email }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span :class="['badge badge-sm', getRoleBadgeClass(user.userData.role)]">
                    {{ user.userData.role }}
                  </span>
                </td>
                <td>
                  <span :class="['badge badge-sm', user.userData.isTemporary ? 'badge-warning' : 'badge-info']">
                    {{ user.userData.isTemporary ? 'Temporary' : 'Authenticated' }}
                  </span>
                </td>
                <td class="font-mono text-xs">{{ user.socketId.substring(0, 8) }}...</td>
                <td>
                  <div class="badge badge-success badge-sm">Online</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Event Types Chart -->
    <div class="card bg-base-100 shadow-xl mb-6">
      <div class="card-body">
        <h3 class="card-title mb-4">Event Types Distribution</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div v-for="(count, eventType) in metrics.eventsByType" :key="eventType" 
               class="stat bg-base-200 rounded-lg p-4">
            <div class="stat-title text-xs">{{ eventType }}</div>
            <div class="stat-value text-lg">{{ count }}</div>
            <div class="stat-desc">{{ getEventPercentage(count) }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Socket Logs -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <div class="flex justify-between items-center mb-4">
          <h3 class="card-title">Recent Socket.io Logs</h3>
          <div class="flex gap-2">
            <select v-model="logLevel" @change="fetchLogs" class="select select-sm select-bordered">
              <option value="">All Levels</option>
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
            <button @click="fetchLogs" class="btn btn-sm btn-outline">Refresh</button>
          </div>
        </div>
        
        <div class="overflow-x-auto max-h-96">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Time</th>
                <th>Level</th>
                <th>Event</th>
                <th>User</th>
                <th>Details</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="log in recentLogs" :key="log.id">
                <td class="text-xs font-mono">{{ formatTime(log.timestamp) }}</td>
                <td>
                  <span :class="['badge badge-sm', getLogLevelClass(log.level)]">
                    {{ log.level }}
                  </span>
                </td>
                <td class="text-sm">{{ log.event || log.message }}</td>
                <td class="text-xs">{{ log.userId || 'System' }}</td>
                <td class="text-xs opacity-70">{{ getLogDetails(log) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSocket } from '@/composables/useSocket'
import { useBackgroundTasks } from '@/composables/useBackgroundTasks'

// Socket connection
const socket = useSocket()
const backgroundTasks = useBackgroundTasks()
const { isConnected } = socket

// Data
const metrics = ref({})
const onlineUsers = ref([])
const recentLogs = ref([])
const logLevel = ref('')
const lastUpdate = ref('')
const memoryStats = ref({})
const memoryHealth = ref('Healthy')
const memoryWarnings = ref(0)

// Computed
const connectionStatus = computed(() => {
  return isConnected.value ? 'Real-time monitoring active' : 'Monitoring unavailable'
})

// Add isConnected computed for template usage
const isConnectedStatus = computed(() => isConnected.value)

const eventsPerSecond = computed(() => {
  return metrics.value.eventsPerSecond ? metrics.value.eventsPerSecond.toFixed(2) : '0.00'
})

const responseTime = computed(() => {
  return metrics.value.averageResponseTime ? Math.round(metrics.value.averageResponseTime) : 0
})

const memoryUsage = computed(() => {
  const usage = memoryStats.value.memory?.heapUsed || 0
  return typeof usage === 'number' ? usage.toFixed(2) : '0.00'
})

const heapUsage = computed(() => {
  const usage = memoryStats.value.process?.heapUsed || 0
  return typeof usage === 'number' ? usage.toFixed(2) : '0.00'
})

const memoryHealthClass = computed(() => {
  const health = memoryHealth.value.toLowerCase()
  if (health === 'healthy') return 'text-success'
  if (health === 'warning') return 'text-warning'
  return 'text-error'
})

// Methods
const fetchMetrics = async () => {
  try {
    // Check authorization before making API calls
    const token = localStorage.getItem('token')
    if (!token) {
      console.log('❌ No token available - skipping metrics fetch')
      return
    }

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'

    const response = await fetch(`${apiUrl}/api/v1/socket/metrics`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    if (data.success) {
      metrics.value = data.data
      lastUpdate.value = new Date().toLocaleTimeString()
    } else {
      console.warn('Metrics fetch unsuccessful:', data.error)
    }
  } catch (error) {
    console.error('Failed to fetch metrics:', error)
    // Set fallback data
    metrics.value = {
      activeConnections: 0,
      totalConnections: 0,
      totalEvents: 0,
      peakConnections: 0,
      averageResponseTime: 0,
      eventsPerSecond: 0,
      eventsByType: {}
    }
  }
}

const refreshUsers = async () => {
  try {
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'
    const response = await fetch(`${apiUrl}/api/v1/socket/users/online`)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    if (data.success) {
      onlineUsers.value = data.users || []
    } else {
      console.warn('Users fetch unsuccessful:', data.error)
      onlineUsers.value = []
    }
  } catch (error) {
    console.error('Failed to fetch users:', error)
    onlineUsers.value = []
  }
}

const fetchLogs = async () => {
  try {
    const token = localStorage.getItem('token')
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'
    const params = new URLSearchParams({ limit: '50' })
    if (logLevel.value) params.append('level', logLevel.value)

    const response = await fetch(`${apiUrl}/api/v1/socket/logs?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    if (data.success) {
      recentLogs.value = data.data.logs || []
    } else {
      console.warn('Logs fetch unsuccessful:', data.error)
      recentLogs.value = []
    }
  } catch (error) {
    console.error('Failed to fetch logs:', error)
    recentLogs.value = []
  }
}

const getRoleBadgeClass = (role) => {
  const classes = {
    admin: 'badge-error',
    user: 'badge-info',
    guest: 'badge-warning'
  }
  return classes[role] || 'badge-neutral'
}

const getLogLevelClass = (level) => {
  const classes = {
    error: 'badge-error',
    warn: 'badge-warning',
    info: 'badge-info',
    debug: 'badge-neutral'
  }
  return classes[level] || 'badge-neutral'
}

const getEventPercentage = (count) => {
  const total = Object.values(metrics.value.eventsByType || {}).reduce((sum, c) => sum + c, 0)
  return total > 0 ? Math.round((count / total) * 100) : 0
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getLogDetails = (log) => {
  if (log.socketId) return `Socket: ${log.socketId.substring(0, 8)}...`
  if (log.ip) return `IP: ${log.ip}`
  if (log.event) return `Event: ${log.event}`
  return 'System log'
}

const fetchMemoryStats = async () => {
  try {
    // Check authorization before making API calls
    const token = localStorage.getItem('token')
    if (!token) {
      console.log('❌ No token available - skipping memory stats fetch')
      return
    }

    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'

    const response = await fetch(`${apiUrl}/api/v1/socket/memory`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    if (data.success) {
      memoryStats.value = data.data
      memoryHealth.value = data.data.health?.status || 'Unknown'
      memoryWarnings.value = data.data.health?.warnings?.length || 0
    } else {
      console.warn('Memory stats fetch unsuccessful:', data.error)
    }
  } catch (error) {
    console.error('Failed to fetch memory stats:', error)
    memoryStats.value = {}
    memoryHealth.value = 'Error'
    memoryWarnings.value = 0
  }
}

const forceCleanup = async () => {
  try {
    const token = localStorage.getItem('token')
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'

    const response = await fetch(`${apiUrl}/api/v1/socket/cleanup`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    if (data.success) {
      console.log('Memory cleanup completed:', data.data)
      // Refresh memory stats after cleanup
      await fetchMemoryStats()
      alert('Memory cleanup completed successfully!')
    } else {
      console.warn('Memory cleanup unsuccessful:', data.error)
      alert('Memory cleanup failed: ' + data.error?.message)
    }
  } catch (error) {
    console.error('Failed to force cleanup:', error)
    alert('Failed to force cleanup: ' + error.message)
  }
}

// Lifecycle - background tasks are now managed by useBackgroundTasks composable

onMounted(() => {
  // Check authorization before initializing
  const token = localStorage.getItem('token')
  if (!token) {
    console.log('❌ No token available - skipping SocketMonitor initialization')
    return
  }

  // Initial data fetch
  fetchMetrics()
  refreshUsers()
  fetchLogs()
  fetchMemoryStats()

  // Register background task for polling (pauses when page is hidden)
  backgroundTasks.registerTask(
    'socket-monitor-polling',
    () => {
      // Check token and network status on each interval
      const currentToken = localStorage.getItem('token')
      if (currentToken && navigator.onLine) {
        fetchMetrics()
        refreshUsers()
        fetchMemoryStats()
      } else if (!currentToken) {
        console.log('❌ Token no longer available - stopping SocketMonitor polling')
        backgroundTasks.unregisterTask('socket-monitor-polling')
      } else if (!navigator.onLine) {
        console.log('🌐 Offline - skipping SocketMonitor polling')
      }
    },
    10000, // 10 seconds
    { pauseWhenHidden: true }
  )

  // Listen for real-time updates via Socket.io
  if (socket.socket.value) {
    // Real-time user updates
    socket.socket.value.on('users:update', (data) => {
      onlineUsers.value = data.users
      lastUpdate.value = new Date().toLocaleTimeString()
    })

    // Real-time metrics updates
    socket.socket.value.on('metrics:update', (data) => {
      metrics.value = data
      lastUpdate.value = new Date().toLocaleTimeString()
    })

    // Real-time memory stats updates
    socket.socket.value.on('memory:update', (data) => {
      memoryStats.value = data
      memoryHealth.value = data.health || 'Healthy'
      memoryWarnings.value = data.warnings || 0
      lastUpdate.value = new Date().toLocaleTimeString()
    })

    // Real-time log updates
    socket.socket.value.on('logs:new', (logEntry) => {
      // Add new log entry to the beginning of the array
      recentLogs.value.unshift(logEntry)
      // Keep only the latest 50 logs
      if (recentLogs.value.length > 50) {
        recentLogs.value = recentLogs.value.slice(0, 50)
      }
      lastUpdate.value = new Date().toLocaleTimeString()
    })

    // Connection status updates
    socket.socket.value.on('connection:status', (data) => {
      // Update connection metrics in real-time
      if (data.activeConnections !== undefined) {
        metrics.value.activeConnections = data.activeConnections
      }
      if (data.totalConnections !== undefined) {
        metrics.value.totalConnections = data.totalConnections
      }
      lastUpdate.value = new Date().toLocaleTimeString()
    })

    // Request real-time updates
    socket.socket.value.emit('admin:subscribe', {
      events: ['users', 'metrics', 'memory', 'logs', 'connections']
    })
  }
})

onUnmounted(() => {
  // Unregister background tasks
  backgroundTasks.unregisterTask('socket-monitor-polling')

  // Clean up socket listeners
  if (socket.socket.value) {
    socket.socket.value.off('users:update')
    socket.socket.value.off('metrics:update')
    socket.socket.value.off('memory:update')
    socket.socket.value.off('logs:new')
    socket.socket.value.off('connection:status')

    // Unsubscribe from real-time updates
    socket.socket.value.emit('admin:unsubscribe')
  }
})
</script>

<style scoped>
.socket-monitor {
  @apply p-6;
}

.stat {
  @apply text-center;
}

.table th {
  @apply text-xs font-semibold;
}

.table td {
  @apply py-2;
}
</style>
