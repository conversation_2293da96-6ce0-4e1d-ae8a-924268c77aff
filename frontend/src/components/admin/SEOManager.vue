<template>
  <div class="seo-manager space-y-6">
    <!-- SEO Overview -->
    <div class="card bg-base-100 shadow-xl glass-effect">
      <div class="card-body">
        <h2 class="card-title text-2xl mb-4">
          <Icon name="search" class="w-6 h-6" />
          SEO Management
        </h2>
        
        <!-- SEO Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Sitemap Status</div>
            <div class="stat-value text-success">✓ Active</div>
            <div class="stat-desc">Last updated: {{ lastSitemapUpdate }}</div>
          </div>
          
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Robots.txt</div>
            <div class="stat-value text-success">✓ Valid</div>
            <div class="stat-desc">Configured for SEO</div>
          </div>
          
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Meta Tags</div>
            <div class="stat-value text-success">✓ Optimized</div>
            <div class="stat-desc">All pages covered</div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-2 mb-4">
          <button @click="generateSitemap" class="btn btn-primary btn-sm">
            <Icon name="refresh" class="w-4 h-4 mr-2" />
            Regenerate Sitemap
          </button>
          <button @click="validateSEO" class="btn btn-secondary btn-sm">
            <Icon name="check" class="w-4 h-4 mr-2" />
            Validate SEO
          </button>
          <button @click="downloadSitemap" class="btn btn-outline btn-sm">
            <Icon name="download" class="w-4 h-4 mr-2" />
            Download Sitemap
          </button>
        </div>
      </div>
    </div>

    <!-- Sitemap Generator -->
    <div class="card bg-base-100 shadow-xl glass-effect">
      <div class="card-body">
        <h3 class="card-title mb-4">Sitemap Configuration</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Base URL</span>
            </label>
            <input 
              v-model="sitemapConfig.baseUrl" 
              type="url" 
              class="input input-bordered" 
              placeholder="https://hlenergy.com"
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">Default Change Frequency</span>
            </label>
            <select v-model="sitemapConfig.defaultChangefreq" class="select select-bordered">
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="yearly">Yearly</option>
            </select>
          </div>
        </div>

        <div class="form-control mt-4">
          <label class="label cursor-pointer">
            <span class="label-text">Include Language Alternates</span>
            <input 
              v-model="sitemapConfig.includeAlternates" 
              type="checkbox" 
              class="checkbox checkbox-primary" 
            />
          </label>
        </div>

        <div class="mt-4">
          <h4 class="font-semibold mb-2">Supported Languages</h4>
          <div class="flex flex-wrap gap-2">
            <div v-for="lang in sitemapConfig.languages" :key="lang" class="badge badge-primary">
              {{ lang.toUpperCase() }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- SEO Analysis -->
    <div class="card bg-base-100 shadow-xl glass-effect">
      <div class="card-body">
        <h3 class="card-title mb-4">SEO Analysis</h3>
        
        <div class="space-y-4">
          <div v-for="page in seoAnalysis" :key="page.url" class="border rounded-lg p-4">
            <div class="flex justify-between items-start mb-2">
              <h4 class="font-semibold">{{ page.title }}</h4>
              <div class="badge" :class="getScoreBadgeClass(page.score)">
                {{ page.score }}/100
              </div>
            </div>
            
            <p class="text-sm text-base-content/70 mb-2">{{ page.url }}</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
              <div class="flex items-center gap-2">
                <Icon :name="page.hasTitle ? 'check' : 'x'" 
                      :class="page.hasTitle ? 'text-success' : 'text-error'" 
                      class="w-4 h-4" />
                <span>Title Tag</span>
              </div>
              
              <div class="flex items-center gap-2">
                <Icon :name="page.hasDescription ? 'check' : 'x'" 
                      :class="page.hasDescription ? 'text-success' : 'text-error'" 
                      class="w-4 h-4" />
                <span>Meta Description</span>
              </div>
              
              <div class="flex items-center gap-2">
                <Icon :name="page.hasStructuredData ? 'check' : 'x'" 
                      :class="page.hasStructuredData ? 'text-success' : 'text-error'" 
                      class="w-4 h-4" />
                <span>Structured Data</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Structured Data Preview -->
    <div class="card bg-base-100 shadow-xl glass-effect">
      <div class="card-body">
        <h3 class="card-title mb-4">Structured Data Preview</h3>
        
        <div class="tabs tabs-boxed mb-4">
          <button 
            v-for="type in structuredDataTypes" 
            :key="type"
            @click="selectedStructuredDataType = type"
            class="tab"
            :class="{ 'tab-active': selectedStructuredDataType === type }"
          >
            {{ type }}
          </button>
        </div>

        <div class="mockup-code">
          <pre><code>{{ getStructuredDataPreview() }}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Icon from '@/components/common/Icon.vue'
import { sitemapGenerator } from '@/utils/sitemapGenerator'
import { useSEO } from '@/composables/useSEO'

// SEO composable
const { generateBreadcrumbStructuredData, generateFAQStructuredData, generateServiceStructuredData } = useSEO()

// Reactive data
const lastSitemapUpdate = ref(new Date().toLocaleDateString())
const selectedStructuredDataType = ref('Organization')

const sitemapConfig = ref({
  baseUrl: 'https://hlenergy.com',
  defaultChangefreq: 'weekly' as const,
  includeAlternates: true,
  languages: ['en', 'es', 'pt']
})

const structuredDataTypes = ['Organization', 'Breadcrumb', 'FAQ', 'Service']

// SEO Analysis data
const seoAnalysis = ref([
  {
    url: '/',
    title: 'HLenergy - Energy Consultation Services',
    score: 95,
    hasTitle: true,
    hasDescription: true,
    hasStructuredData: true
  },
  {
    url: '/about',
    title: 'About HLenergy',
    score: 88,
    hasTitle: true,
    hasDescription: true,
    hasStructuredData: false
  },
  {
    url: '/services',
    title: 'Our Services',
    score: 92,
    hasTitle: true,
    hasDescription: true,
    hasStructuredData: true
  },
  {
    url: '/contact',
    title: 'Contact Us',
    score: 85,
    hasTitle: true,
    hasDescription: true,
    hasStructuredData: false
  }
])

// Methods
const generateSitemap = () => {
  try {
    const sitemap = sitemapGenerator.generateSitemap()
    console.log('Generated sitemap:', sitemap)
    lastSitemapUpdate.value = new Date().toLocaleDateString()
    alert('Sitemap generated successfully!')
  } catch (error) {
    console.error('Failed to generate sitemap:', error)
    alert('Failed to generate sitemap')
  }
}

const validateSEO = () => {
  // Simulate SEO validation
  console.log('Validating SEO...')
  alert('SEO validation completed! Check console for details.')
}

const downloadSitemap = () => {
  try {
    const sitemap = sitemapGenerator.generateSitemap()
    const blob = new Blob([sitemap], { type: 'application/xml' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sitemap.xml'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download sitemap:', error)
    alert('Failed to download sitemap')
  }
}

const getScoreBadgeClass = (score: number) => {
  if (score >= 90) return 'badge-success'
  if (score >= 70) return 'badge-warning'
  return 'badge-error'
}

const getStructuredDataPreview = () => {
  switch (selectedStructuredDataType.value) {
    case 'Organization':
      return JSON.stringify(sitemapGenerator.generateOrganizationStructuredData(), null, 2)
    case 'Breadcrumb':
      return JSON.stringify(generateBreadcrumbStructuredData([
        { name: 'Home', url: '/' },
        { name: 'Services', url: '/services' },
        { name: 'Energy Audit', url: '/services/energy-audit' }
      ]), null, 2)
    case 'FAQ':
      return JSON.stringify(generateFAQStructuredData([
        {
          question: 'What is an energy audit?',
          answer: 'An energy audit is a comprehensive assessment of your energy usage to identify opportunities for improvement and cost savings.'
        }
      ]), null, 2)
    case 'Service':
      return JSON.stringify(generateServiceStructuredData({
        name: 'Energy Audit',
        description: 'Comprehensive energy assessment and optimization recommendations',
        serviceType: 'Energy Consulting'
      }), null, 2)
    default:
      return '{}'
  }
}

onMounted(() => {
  console.log('SEO Manager initialized')
})
</script>

<style scoped>
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}
</style>
