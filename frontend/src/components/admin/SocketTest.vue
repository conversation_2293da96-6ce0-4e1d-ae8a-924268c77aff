<template>
  <div class="space-y-6">
    <!-- Socket.io Test -->
    <div class="bg-base-100 rounded-lg shadow-lg p-4 md:p-6">
      <div class="flex flex-col md:flex-row md:items-center justify-between mb-4 md:mb-6 gap-4">
        <h2 class="text-lg md:text-2xl font-bold flex items-center">
          <Icon name="zap" size="md" class="mr-2 md:mr-3 text-primary" />
          Socket.io Real-time Test
        </h2>

        <!-- Connection Status -->
        <SocketStatus :auto-expand="false" />
      </div>

    <!-- Authentication Status -->
    <div class="bg-base-200 rounded-lg p-4 mb-6">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <Icon name="key" size="md" class="mr-2 text-warning" />
        Authentication Status
      </h3>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="stat bg-base-100 rounded-lg p-3">
          <div class="stat-title text-xs">Auth Status</div>
          <div class="stat-value text-sm" :class="{
            'text-success': authStore.isAuthenticated,
            'text-error': !authStore.isAuthenticated
          }">
            {{ authStore.isAuthenticated ? 'Authenticated' : 'Not Authenticated' }}
          </div>
        </div>

        <div class="stat bg-base-100 rounded-lg p-3">
          <div class="stat-title text-xs">User</div>
          <div class="stat-value text-sm">
            {{ getUserDisplay() }}
          </div>
          <div class="stat-desc text-xs" v-if="!authStore.isAuthenticated && socket.tempToken?.value">
            Guest User
          </div>
        </div>

        <div class="stat bg-base-100 rounded-lg p-3">
          <div class="stat-title text-xs">Token</div>
          <div class="stat-value text-xs font-mono">
            {{ getTokenDisplay() }}
          </div>
          <div class="stat-desc text-xs" v-if="socket.tempToken">
            {{ getTokenTypeDisplay() }}
          </div>
        </div>
      </div>

      <div class="flex gap-2 mt-4">
        <button
          v-if="!authStore.isAuthenticated"
          @click="testLogin"
          class="btn btn-primary btn-sm"
        >
          <Icon name="log-in" size="sm" class="mr-2" />
          Test Login
        </button>

        <button
          v-if="authStore.isAuthenticated"
          @click="authStore.logout"
          class="btn btn-error btn-sm"
        >
          <Icon name="log-out" size="sm" class="mr-2" />
          Logout
        </button>

        <button
          v-if="authStore.isAuthenticated && !authStore.isLocked"
          @click="testLockSession"
          class="btn btn-warning btn-sm"
        >
          <Icon name="lock" size="sm" class="mr-2" />
          Test Lock Session
        </button>

        <button
          v-if="!socket.isConnected"
          @click="connectSocket"
          class="btn btn-secondary btn-sm"
        >
          <Icon name="wifi" size="sm" class="mr-2" />
          Connect Socket
        </button>

        <button
          v-if="socket.isConnected"
          @click="socket.disconnect"
          class="btn btn-error btn-sm"
        >
          <Icon name="wifi-off" size="sm" class="mr-2" />
          Disconnect
        </button>

        <button
          v-if="!authStore.isAuthenticated"
          @click="refreshToken"
          class="btn btn-info btn-sm"
          :disabled="socket.isGettingTempToken?.value"
        >
          <Icon name="refresh" size="sm" class="mr-2" />
          {{ socket.isGettingTempToken?.value ? 'Refreshing...' : 'Refresh Token' }}
        </button>
      </div>
    </div>

    <!-- Real-time Statistics -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">Connection</div>
        <div class="stat-value text-lg" :class="{
          'text-success': socket.isConnected,
          'text-warning': socket.connectionStatus.value === 'no-auth',
          'text-info': socket.connectionStatus.value === 'connecting',
          'text-error': socket.connectionStatus.value === 'error',
          'text-base-content': socket.connectionStatus.value === 'disconnected'
        }">
          {{ getConnectionStatusText() }}
        </div>
      </div>

      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">Events Logged</div>
        <div class="stat-value text-lg">{{ eventLog.length }}</div>
      </div>

      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">Online Users</div>
        <div class="stat-value text-lg">{{ socket.onlineUsers?.value?.length || 0 }}</div>
      </div>

      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">Notifications</div>
        <div class="stat-value text-lg">{{ socket.unreadNotifications || 0 }}</div>
      </div>
    </div>

    <!-- Test Controls -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Analytics Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="chart-line" size="md" class="mr-2 text-primary" />
          Analytics Events
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="sendPageView"
            class="btn btn-primary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="eye" size="sm" class="mr-2" />
            Send Page View
          </button>
          
          <button 
            @click="sendClickEvent"
            class="btn btn-secondary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="cursor-click" size="sm" class="mr-2" />
            Send Click Event
          </button>
          
          <button 
            @click="sendFormSubmit"
            class="btn btn-accent btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="form" size="sm" class="mr-2" />
            Send Form Submit
          </button>
        </div>
      </div>

      <!-- Communication Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="message-circle" size="md" class="mr-2 text-secondary" />
          Communication
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="sendTestMessage"
            class="btn btn-primary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="send" size="sm" class="mr-2" />
            Send Test Message
          </button>
          
          <button 
            @click="joinTestRoom"
            class="btn btn-secondary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="users" size="sm" class="mr-2" />
            Join Test Room
          </button>
          
          <button 
            @click="startTyping"
            class="btn btn-accent btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="edit" size="sm" class="mr-2" />
            Start Typing
          </button>
        </div>
      </div>

      <!-- Notifications Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="bell" size="md" class="mr-2 text-warning" />
          Notifications
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="sendNotification"
            class="btn btn-warning btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="bell" size="sm" class="mr-2" />
            Send Notification
          </button>
          
          <button 
            @click="sendUrgentAlert"
            class="btn btn-error btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="alert-triangle" size="sm" class="mr-2" />
            Send Urgent Alert
          </button>
          
          <div class="text-sm text-base-content/70">
            Unread: {{ socket.unreadNotifications || 0 }}
          </div>
        </div>
      </div>

      <!-- API Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="server" size="md" class="mr-2 text-info" />
          API Tests
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="testSocketAPI"
            class="btn btn-info btn-sm w-full"
          >
            <Icon name="activity" size="sm" class="mr-2" />
            Test Socket API
          </button>
          
          <button 
            @click="getOnlineUsers"
            class="btn btn-success btn-sm w-full"
          >
            <Icon name="users" size="sm" class="mr-2" />
            Get Online Users
          </button>
          
          <button 
            @click="broadcastMessage"
            class="btn btn-primary btn-sm w-full"
          >
            <Icon name="broadcast" size="sm" class="mr-2" />
            Broadcast Message
          </button>
        </div>
      </div>
    </div>

    <!-- Event Log -->
    <div class="mt-6 bg-base-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <Icon name="list" size="md" class="mr-2 text-neutral" />
        Event Log
        <button 
          @click="clearLog"
          class="btn btn-ghost btn-xs ml-auto"
        >
          Clear
        </button>
      </h3>
      
      <div class="bg-base-100 rounded p-3 max-h-60 overflow-y-auto">
        <div 
          v-for="(event, index) in eventLog" 
          :key="index"
          class="text-sm font-mono mb-2 p-2 rounded"
          :class="{
            'bg-success/20 text-success': event.type === 'success',
            'bg-error/20 text-error': event.type === 'error',
            'bg-info/20 text-info': event.type === 'info',
            'bg-warning/20 text-warning': event.type === 'warning'
          }"
        >
          <div class="flex justify-between items-start">
            <span class="font-semibold">{{ event.title }}</span>
            <span class="text-xs opacity-70">{{ formatTime(event.timestamp) }}</span>
          </div>
          <div class="mt-1 opacity-80">{{ event.message }}</div>
          <div v-if="event.data" class="mt-1 text-xs opacity-60">
            {{ JSON.stringify(event.data, null, 2) }}
          </div>
        </div>
        
        <div v-if="eventLog.length === 0" class="text-center py-4 text-base-content/60">
          No events yet. Try the buttons above!
        </div>
      </div>
    </div>
    </div>

    <!-- PWA Test Controls -->
    <PWATestControls />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSocket } from '@/composables/useSocket'
import { useAuthStore } from '@/stores/auth'
import SocketStatus from '@/components/common/SocketStatus.vue'
import Icon from '@/components/common/Icon.vue'
import PWATestControls from '@/components/admin/PWATestControls.vue'

// Socket integration
const socket = useSocket()

// Auth store
const authStore = useAuthStore()

// Event log
const eventLog = ref<any[]>([])

// Helper functions
const addToLog = (type: string, title: string, message: string, data?: any) => {
  eventLog.value.unshift({
    type,
    title,
    message,
    data,
    timestamp: new Date().toISOString()
  })
  
  // Keep only last 50 events
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

const clearLog = () => {
  eventLog.value = []
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getConnectionStatusText = () => {
  switch (socket.connectionStatus.value) {
    case 'connected':
      return 'Online'
    case 'connecting':
      return 'Connecting...'
    case 'error':
      return 'Error'
    case 'no-auth':
      return 'No Auth'
    case 'disconnected':
      return 'Offline'
    default:
      return 'Unknown'
  }
}

const getTokenDisplay = () => {
  if (authStore.token) {
    return authStore.token.substring(0, 20) + '...'
  } else if (socket.tempToken?.value) {
    return socket.tempToken.value.substring(0, 20) + '...'
  }
  return 'Auto-generating...'
}

const getTokenTypeDisplay = () => {
  if (authStore.token) {
    return 'User Token'
  } else if (socket.tempToken?.value) {
    const expiry = socket.tempTokenExpiry?.value
    if (expiry) {
      const remaining = Math.max(0, Math.floor((expiry - Date.now()) / (1000 * 60)))
      return `Temp Token (${remaining}min left)`
    }
    return 'Temporary Token'
  }
  return 'No Token'
}

const getUserDisplay = () => {
  if (authStore.user?.email) {
    return authStore.user.email
  } else if (socket.tempToken?.value) {
    // Extract the random name from the temp token by decoding it
    try {
      const payload = JSON.parse(atob(socket.tempToken.value.split('.')[1]))
      return payload.name || payload.email?.split('@')[0] || 'Guest User'
    } catch (error) {
      return 'Guest User'
    }
  }
  return 'Not Connected'
}

// Authentication functions
const testLogin = async () => {
  try {
    addToLog('info', 'Test Login', 'Attempting test login...')

    // Use test credentials
    await authStore.login({
      email: '<EMAIL>',
      password: 'password123'
    })

    addToLog('success', 'Login Success', 'Test login successful')

    // Connect socket after login
    setTimeout(() => {
      connectSocket()
    }, 1000)

  } catch (error: any) {
    addToLog('error', 'Login Failed', error.message)
  }
}

const testLockSession = () => {
  try {
    addToLog('info', 'Test Lock Session', 'Manually locking session for testing...')
    authStore.lockSession()
    addToLog('success', 'Session Locked', 'Session has been locked. Use PIN or biometric to unlock.')
  } catch (error: any) {
    addToLog('error', 'Lock Failed', error.message)
  }
}

const refreshToken = async () => {
  try {
    addToLog('info', 'Refreshing Token', 'Manually refreshing temporary token...')

    const success = await socket.getTempToken()

    if (success) {
      addToLog('success', 'Token Refreshed', 'New temporary token obtained successfully')

      // Reconnect if not connected
      if (!socket.isConnected.value) {
        setTimeout(() => {
          connectSocket()
        }, 500)
      }
    } else {
      addToLog('error', 'Token Refresh Failed', 'Failed to refresh temporary token')
    }
  } catch (error: any) {
    addToLog('error', 'Token Refresh Error', error.message)
  }
}

const connectSocket = async () => {
  addToLog('info', 'Connecting Socket', 'Connecting to Socket.io (will auto-get token if needed)...')
  await socket.connect()
}

// Analytics test functions
const sendPageView = () => {
  if (!socket.isConnected.value) {
    addToLog('error', 'Not Connected', 'Socket.io is not connected')
    return
  }

  if (socket.trackPageView) {
    socket.trackPageView('/test-page', 'Socket.io Test Page')
    addToLog('success', 'Page View Sent', 'Tracked page view for /test-page via Socket.io')
  } else {
    addToLog('error', 'Method Missing', 'trackPageView method not available')
  }
}

const sendClickEvent = () => {
  if (!socket.isConnected.value) {
    addToLog('error', 'Not Connected', 'Socket.io is not connected')
    return
  }

  if (socket.trackClick) {
    socket.trackClick('test-button', '/test-page')
    addToLog('success', 'Click Event Sent', 'Tracked click on test-button via Socket.io')
  } else {
    addToLog('error', 'Method Missing', 'trackClick method not available')
  }
}

const sendFormSubmit = () => {
  if (!socket.isConnected.value) {
    addToLog('error', 'Not Connected', 'Socket.io is not connected')
    return
  }

  if (socket.trackFormSubmit) {
    socket.trackFormSubmit('test-form', true)
    addToLog('success', 'Form Submit Sent', 'Tracked successful form submission via Socket.io')
  } else {
    addToLog('error', 'Method Missing', 'trackFormSubmit method not available')
  }
}

// Communication test functions
const sendTestMessage = () => {
  if (!socket.isConnected.value) {
    addToLog('error', 'Not Connected', 'Socket.io is not connected')
    return
  }

  if (socket.sendMessage) {
    socket.sendMessage('test-conversation', 'Hello from Socket.io test!')
    addToLog('info', 'Message Sent', 'Sent test message to conversation via Socket.io')
  } else {
    addToLog('error', 'Method Missing', 'sendMessage method not available')
  }
}

const joinTestRoom = () => {
  if (!socket.isConnected.value) {
    addToLog('error', 'Not Connected', 'Socket.io is not connected')
    return
  }

  if (socket.joinRoom) {
    socket.joinRoom('test-room')
    addToLog('info', 'Room Joined', 'Joined test-room via Socket.io')
  } else {
    addToLog('error', 'Method Missing', 'joinRoom method not available')
  }
}

const startTyping = () => {
  if (!socket.isConnected.value) {
    addToLog('error', 'Not Connected', 'Socket.io is not connected')
    return
  }

  if (socket.startTyping && socket.stopTyping) {
    socket.startTyping('test-room')
    addToLog('info', 'Typing Started', 'Started typing in test-room via Socket.io')

    // Stop typing after 3 seconds
    setTimeout(() => {
      socket.stopTyping('test-room')
      addToLog('info', 'Typing Stopped', 'Stopped typing in test-room via Socket.io')
    }, 3000)
  } else {
    addToLog('error', 'Method Missing', 'startTyping/stopTyping methods not available')
  }
}

// Notification test functions
const sendNotification = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'info',
        title: 'Test Notification',
        message: 'This is a test notification from the Socket.io test interface',
        priority: 'medium'
      })
    })

    const data = await response.json()
    addToLog('success', 'Notification Sent', `Notification sent to ${data.recipients} users`, data)
  } catch (error: any) {
    addToLog('error', 'Notification Failed', error.message)
  }
}

const sendUrgentAlert = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'warning',
        title: '🚨 Urgent Alert',
        message: 'This is an urgent test alert from the Socket.io test interface',
        priority: 'urgent'
      })
    })

    const data = await response.json()
    addToLog('warning', 'Urgent Alert Sent', `Alert sent to ${data.recipients} users`, data)
  } catch (error: any) {
    addToLog('error', 'Alert Failed', error.message)
  }
}

// API test functions
const testSocketAPI = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: 'Test message from frontend'
      })
    })
    
    const data = await response.json()
    addToLog('success', 'API Test Success', 'Socket API test completed', data)
  } catch (error: any) {
    addToLog('error', 'API Test Failed', error.message)
  }
}

const getOnlineUsers = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/users/online')
    const data = await response.json()
    addToLog('info', 'Online Users', `Found ${data.count} online users`, data.users)
  } catch (error: any) {
    addToLog('error', 'Failed to get users', error.message)
  }
}

const broadcastMessage = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/broadcast', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event: 'test:broadcast',
        message: 'Hello from frontend broadcast test!'
      })
    })
    
    const data = await response.json()
    addToLog('success', 'Broadcast Sent', `Message sent to ${data.recipients} users`, data)
  } catch (error: any) {
    addToLog('error', 'Broadcast Failed', error.message)
  }
}

// Real-time event listeners
const setupEventListeners = () => {
  if (!socket.socket) return

  // Connection events
  socket.socket.on('connect', () => {
    addToLog('success', 'Connected', 'Successfully connected to Socket.io server')
  })

  socket.socket.on('disconnect', (reason) => {
    addToLog('warning', 'Disconnected', `Disconnected from server: ${reason}`)
  })

  socket.socket.on('connect_error', (error) => {
    addToLog('error', 'Connection Error', `Failed to connect: ${error.message}`)
  })

  // Test message events
  socket.socket.on('test:message', (data) => {
    addToLog('info', 'Test Message Received', 'Received test message from server', data)
  })

  socket.socket.on('test:broadcast', (data) => {
    addToLog('info', 'Broadcast Received', 'Received broadcast message', data)
  })

  // Analytics events
  socket.socket.on('analytics:realtime_pageview', (event) => {
    addToLog('info', 'Analytics: Page View', 'Real-time page view event received', event)
  })

  socket.socket.on('analytics:realtime_click', (event) => {
    addToLog('info', 'Analytics: Click', 'Real-time click event received', event)
  })

  socket.socket.on('analytics:realtime_form', (event) => {
    addToLog('info', 'Analytics: Form', 'Real-time form event received', event)
  })

  socket.socket.on('analytics:visitor_count', (data) => {
    addToLog('info', 'Visitor Count', `${data.count} users online`, data)
  })

  // Communication events
  socket.socket.on('communication:message_received', (message) => {
    addToLog('info', 'Message Received', 'New message received', message)
  })

  socket.socket.on('communication:auto_response', (response) => {
    addToLog('info', 'Auto Response', 'Auto response received', response)
  })

  // Notification events
  socket.socket.on('notification:received', (notification) => {
    addToLog('warning', 'Notification', notification.title, notification)
  })

  socket.socket.on('notification:urgent', (notification) => {
    addToLog('error', 'Urgent Notification', notification.title, notification)
  })

  // Typing events
  socket.socket.on('typing:user_started', (data) => {
    addToLog('info', 'User Started Typing', `${data.userData.email} started typing`)
  })

  socket.socket.on('typing:user_stopped', (data) => {
    addToLog('info', 'User Stopped Typing', `${data.userData.email} stopped typing`)
  })

  // Room events
  socket.socket.on('room:joined', (data) => {
    addToLog('success', 'Room Joined', `Joined room: ${data.roomId}`)
  })

  socket.socket.on('room:left', (data) => {
    addToLog('info', 'Room Left', `Left room: ${data.roomId}`)
  })

  socket.socket.on('room:user_joined', (data) => {
    addToLog('info', 'User Joined Room', `${data.userData.email} joined the room`)
  })

  socket.socket.on('room:user_left', (data) => {
    addToLog('info', 'User Left Room', `${data.userData.email} left the room`)
  })

  // User presence events
  socket.socket.on('user:online', (data) => {
    addToLog('success', 'User Online', `${data.userData.email} came online`)
  })

  socket.socket.on('user:offline', (data) => {
    addToLog('warning', 'User Offline', `${data.userData.email} went offline`)
  })

  // System events
  socket.socket.on('system:maintenance_notice', (notice) => {
    addToLog('warning', 'Maintenance Notice', notice.message, notice)
  })
}

// Auto-connect on mount and setup listeners
onMounted(() => {
  if (!socket.isConnected.value) {
    socket.connect()
    addToLog('info', 'Connecting', 'Attempting to connect to Socket.io server...')
  }

  // Setup event listeners
  setupEventListeners()

  // Re-setup listeners when socket reconnects
  if (socket.socket) {
    socket.socket.on('connect', setupEventListeners)
  }
})

// CRITICAL: Clean up all event listeners on unmount
onUnmounted(() => {
  console.log('🧹 Cleaning up SocketTest event listeners...')

  if (socket.socket?.value) {
    // Remove all Socket.io event listeners
    socket.socket.value.off('connect')
    socket.socket.value.off('disconnect')
    socket.socket.value.off('connect_error')
    socket.socket.value.off('test:message')
    socket.socket.value.off('test:broadcast')
    socket.socket.value.off('analytics:realtime_pageview')
    socket.socket.value.off('analytics:realtime_click')
    socket.socket.value.off('analytics:realtime_form')
    socket.socket.value.off('analytics:visitor_count')
    socket.socket.value.off('communication:message_received')
    socket.socket.value.off('communication:auto_response')
    socket.socket.value.off('notification:received')
    socket.socket.value.off('notification:urgent')
    socket.socket.value.off('typing:user_started')
    socket.socket.value.off('typing:user_stopped')
    socket.socket.value.off('room:joined')
    socket.socket.value.off('room:left')
    socket.socket.value.off('room:user_joined')
    socket.socket.value.off('room:user_left')
    socket.socket.value.off('user:online')
    socket.socket.value.off('user:offline')
    socket.socket.value.off('system:maintenance_notice')

    console.log('✅ All SocketTest event listeners removed')
  }
})
</script>
