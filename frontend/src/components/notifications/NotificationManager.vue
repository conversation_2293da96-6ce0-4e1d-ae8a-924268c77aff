<template>
  <div class="notification-manager">
    <!-- Notification Settings Card -->
    <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
      <div class="card-body p-6">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-base-content">Push Notifications</h3>
              <p class="text-sm text-base-content/60">Manage your notification preferences</p>
            </div>
          </div>
          
          <!-- Status Indicator -->
          <div class="flex items-center space-x-2">
            <div 
              :class="[
                'w-3 h-3 rounded-full',
                isSubscribed ? 'bg-success animate-pulse' : 'bg-error'
              ]"
            ></div>
            <span class="text-sm font-medium" :class="isSubscribed ? 'text-success' : 'text-error'">
              {{ statusMessage }}
            </span>
          </div>
        </div>

        <!-- Support Check -->
        <div v-if="!permission.supported" class="alert alert-warning mb-4">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
          </svg>
          <span>Push notifications are not supported in this browser.</span>
        </div>

        <!-- Permission Denied -->
        <div v-else-if="isDenied" class="alert alert-error mb-4">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <div>
            <div class="font-semibold">Notifications Blocked</div>
            <div class="text-sm">Please enable notifications in your browser settings to receive updates.</div>
          </div>
        </div>

        <!-- Controls -->
        <div class="space-y-4">
          <!-- Permission Request -->
          <div v-if="canRequestPermission" class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
            <div>
              <div class="font-medium">Enable Notifications</div>
              <div class="text-sm text-base-content/70">Get notified about important energy updates and alerts</div>
            </div>
            <button 
              @click="requestPermission" 
              :disabled="isSubscribing"
              class="btn btn-primary btn-sm"
            >
              <span v-if="isSubscribing" class="loading loading-spinner loading-sm"></span>
              {{ isSubscribing ? 'Requesting...' : 'Enable' }}
            </button>
          </div>

          <!-- Subscription Controls -->
          <div v-if="isGranted" class="space-y-3">
            <div class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
              <div>
                <div class="font-medium">Push Notifications</div>
                <div class="text-sm text-base-content/70">
                  {{ isSubscribed ? 'You will receive push notifications' : 'Subscribe to receive notifications' }}
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button 
                  v-if="!isSubscribed"
                  @click="subscribe" 
                  :disabled="isSubscribing"
                  class="btn btn-success btn-sm"
                >
                  <span v-if="isSubscribing" class="loading loading-spinner loading-sm"></span>
                  {{ isSubscribing ? 'Subscribing...' : 'Subscribe' }}
                </button>
                <button 
                  v-else
                  @click="unsubscribe" 
                  :disabled="isSubscribing"
                  class="btn btn-error btn-sm"
                >
                  <span v-if="isSubscribing" class="loading loading-spinner loading-sm"></span>
                  {{ isSubscribing ? 'Unsubscribing...' : 'Unsubscribe' }}
                </button>
              </div>
            </div>

            <!-- Test Notification -->
            <div v-if="isSubscribed" class="space-y-3">
              <div class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
                <div>
                  <div class="font-medium">Server Test Notification</div>
                  <div class="text-sm text-base-content/70">Send a test notification via server (requires backend)</div>
                </div>
                <button
                  @click="sendTest"
                  :disabled="isSendingTest"
                  class="btn btn-accent btn-sm"
                >
                  <span v-if="isSendingTest" class="loading loading-spinner loading-sm"></span>
                  {{ isSendingTest ? 'Sending...' : 'Server Test' }}
                </button>
              </div>

              <div class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
                <div>
                  <div class="font-medium">Local Test Notification</div>
                  <div class="text-sm text-base-content/70">Send a local notification to test browser permissions</div>
                </div>
                <button
                  @click="sendLocalTest"
                  :disabled="isSendingLocalTest"
                  class="btn btn-primary btn-sm"
                >
                  <span v-if="isSendingLocalTest" class="loading loading-spinner loading-sm"></span>
                  {{ isSendingLocalTest ? 'Sending...' : 'Local Test' }}
                </button>
              </div>

              <div class="flex items-center justify-between p-4 bg-base-200/50 rounded-lg">
                <div>
                  <div class="font-medium">Service Worker Debug</div>
                  <div class="text-sm text-base-content/70">Check service worker status and manually trigger push event</div>
                </div>
                <button
                  @click="debugServiceWorker"
                  class="btn btn-warning btn-sm"
                >
                  Debug SW
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="alert alert-error mt-4">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Send to Specific Users -->
        <div v-if="isSubscribed" class="mt-6 p-4 bg-base-200/30 rounded-lg">
          <div class="text-sm font-medium text-base-content/70 mb-4">Send Targeted Notifications</div>

          <!-- Send to Specific User -->
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-2">Send to Specific User</label>
              <div class="flex gap-2">
                <input
                  v-model="targetUserId"
                  type="number"
                  placeholder="User ID (e.g., 1, 2, 3...)"
                  class="input input-bordered input-sm flex-1"
                >
                <button
                  @click="sendToUser"
                  :disabled="!targetUserId || isSendingToUser"
                  class="btn btn-primary btn-sm"
                >
                  <span v-if="isSendingToUser" class="loading loading-spinner loading-sm"></span>
                  {{ isSendingToUser ? 'Sending...' : 'Send' }}
                </button>
              </div>
            </div>

            <!-- Send to Admins -->
            <div>
              <label class="block text-sm font-medium mb-2">Send to Admin Users</label>
              <button
                @click="sendToAdmins"
                :disabled="isSendingToAdmins"
                class="btn btn-warning btn-sm"
              >
                <span v-if="isSendingToAdmins" class="loading loading-spinner loading-sm"></span>
                {{ isSendingToAdmins ? 'Sending...' : 'Send to Admins' }}
              </button>
            </div>

            <!-- Send System Announcement -->
            <div>
              <label class="block text-sm font-medium mb-2">Send System Announcement</label>
              <button
                @click="sendSystemAnnouncement"
                :disabled="isSendingAnnouncement"
                class="btn btn-accent btn-sm"
              >
                <span v-if="isSendingAnnouncement" class="loading loading-spinner loading-sm"></span>
                {{ isSendingAnnouncement ? 'Sending...' : 'Send Announcement' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Debug Info -->
        <div class="mt-6 p-4 bg-base-200/30 rounded-lg">
          <div class="text-sm font-medium text-base-content/70 mb-2">Debug Information</div>
          <div class="text-xs text-base-content/50 space-y-1">
            <div><strong>Browser Support:</strong> {{ permission.supported ? 'Yes' : 'No' }}</div>
            <div><strong>Permission:</strong> {{ notificationPermission }}</div>
            <div><strong>Service Worker:</strong> Available</div>
            <div><strong>Push Manager:</strong> Available</div>
            <div><strong>Subscribed:</strong> {{ isSubscribed ? 'Yes' : 'No' }}</div>
            <div v-if="isSubscribed && subscription"><strong>Endpoint:</strong> {{ subscription.endpoint.substring(0, 50) }}...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notification Types -->
    <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl mt-6">
      <div class="card-body p-6">
        <h3 class="text-lg font-semibold text-base-content mb-4">Notification Types</h3>
        
        <div class="space-y-3">
          <div v-for="type in notificationTypes" :key="type.id" 
               class="flex items-center justify-between p-3 bg-base-200/30 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center"
                   :class="type.colorClass">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path :d="type.iconPath" />
                </svg>
              </div>
              <div>
                <div class="font-medium">{{ type.name }}</div>
                <div class="text-sm text-base-content/70">{{ type.description }}</div>
              </div>
            </div>
            <input 
              type="checkbox" 
              :checked="type.enabled" 
              @change="toggleNotificationType(type.id)"
              class="checkbox checkbox-primary"
              :disabled="!isSubscribed"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { usePushNotifications } from '@/composables/usePushNotifications'
import { useAuthStore } from '@/stores/auth'

// Use stores
const authStore = useAuthStore()

// Use push notifications composable
const {
  permission,
  subscription,
  isSubscribing,
  error,
  canRequestPermission,
  isGranted,
  isDenied,
  isSubscribed,
  requestPermission,
  subscribe,
  unsubscribe,
  showNotification,
  sendTestNotification
} = usePushNotifications()

// Local state
const isSendingTest = ref(false)
const isSendingLocalTest = ref(false)
const targetUserId = ref('')
const isSendingToUser = ref(false)
const isSendingToAdmins = ref(false)
const isSendingAnnouncement = ref(false)

// Make Notification API available in template
const notificationPermission = computed(() => {
  if (typeof window !== 'undefined' && 'Notification' in window) {
    return Notification.permission
  }
  return 'unsupported'
})

// Notification types configuration
const notificationTypes = ref([
  {
    id: 'energy-alerts',
    name: 'Energy Alerts',
    description: 'High consumption warnings and efficiency tips',
    enabled: true,
    colorClass: 'bg-warning/20 text-warning',
    iconPath: 'M13 10V3L4 14h7v7l9-11h-7z'
  },
  {
    id: 'system-updates',
    name: 'System Updates',
    description: 'Platform updates and maintenance notifications',
    enabled: true,
    colorClass: 'bg-info/20 text-info',
    iconPath: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
  },
  {
    id: 'lead-notifications',
    name: 'New Leads',
    description: 'Notifications when new leads are generated',
    enabled: true,
    colorClass: 'bg-success/20 text-success',
    iconPath: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
  },
  {
    id: 'reports',
    name: 'Reports Ready',
    description: 'When energy reports and analytics are available',
    enabled: false,
    colorClass: 'bg-primary/20 text-primary',
    iconPath: 'M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  }
])

// Computed
const statusMessage = computed(() => {
  if (!permission.value.supported) return 'Not Supported'
  if (isDenied.value) return 'Blocked'
  if (isSubscribed.value) return 'Active'
  if (isGranted.value) return 'Ready'
  return 'Disabled'
})

// Methods
const sendTest = async () => {
  try {
    isSendingTest.value = true

    await sendTestNotification('This is a test notification from your energy management dashboard! 🔋')

    console.log('✅ Test notification sent successfully')
  } catch (err) {
    console.error('❌ Failed to send test notification:', err)
  } finally {
    isSendingTest.value = false
  }
}

const sendLocalTest = async () => {
  try {
    isSendingLocalTest.value = true

    console.log('🔔 Sending local test notification...')

    if (typeof window === 'undefined' || !('Notification' in window)) {
      console.error('❌ Notification API not available')
      alert('Notification API not available in this browser.')
      return
    }

    console.log('📋 Notification permission:', Notification.permission)

    if (Notification.permission !== 'granted') {
      console.error('❌ Notification permission not granted')
      alert('Notification permission not granted. Please enable notifications first.')
      return
    }

    // Test with simple Notification API first
    const simpleNotification = new Notification('HLenergy Local Test', {
      body: 'This is a simple local test notification! 🔋',
      icon: `${window.location.origin}/hl-energy-logo-192w.png`,
      badge: `${window.location.origin}/hl-energy-logo-96w.png`,
      tag: 'local-test-simple'
    })

    simpleNotification.onclick = () => {
      console.log('✅ Simple notification clicked')
      simpleNotification.close()
    }

    // Wait a bit before sending the second notification
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Also test with service worker
    await showNotification('HLenergy Service Worker Test', {
      body: 'This is a service worker test notification! ⚡',
      icon: `${window.location.origin}/hl-energy-logo-192w.png`,
      badge: `${window.location.origin}/hl-energy-logo-96w.png`,
      tag: 'local-test-sw',
      requireInteraction: true,
      actions: [
        { action: 'view', title: 'View Dashboard' },
        { action: 'dismiss', title: 'Dismiss' }
      ]
    })

    console.log('✅ Local test notifications sent')
  } catch (err) {
    console.error('❌ Failed to send local test notification:', err)
    alert(`Failed to send local notification: ${err.message}`)
  } finally {
    isSendingLocalTest.value = false
  }
}

const debugServiceWorker = async () => {
  try {
    console.log('🔍 [DEBUG] Starting service worker debug...')

    // Check if service worker is supported
    if (!('serviceWorker' in navigator)) {
      console.error('❌ Service Worker not supported')
      alert('Service Worker not supported in this browser')
      return
    }

    // Get service worker registration
    const registration = await navigator.serviceWorker.ready
    console.log('✅ [DEBUG] Service Worker registration:', registration)
    console.log('✅ [DEBUG] Service Worker state:', registration.active?.state)
    console.log('✅ [DEBUG] Service Worker script URL:', registration.active?.scriptURL)

    // Check push manager
    if (!registration.pushManager) {
      console.error('❌ Push Manager not available')
      alert('Push Manager not available')
      return
    }

    // Get current subscription
    const subscription = await registration.pushManager.getSubscription()
    console.log('✅ [DEBUG] Current push subscription:', subscription)

    if (!subscription) {
      console.error('❌ No push subscription found')
      alert('No push subscription found. Please subscribe first.')
      return
    }

    console.log('✅ [DEBUG] Subscription endpoint:', subscription.endpoint)
    console.log('✅ [DEBUG] Subscription keys:', {
      p256dh: subscription.getKey('p256dh'),
      auth: subscription.getKey('auth')
    })

    // Try to manually trigger a push event (this won't work in real scenarios, but helps debug)
    console.log('🔔 [DEBUG] Attempting to show notification via service worker...')

    await registration.showNotification('Service Worker Debug Test', {
      body: 'This notification was triggered directly from the service worker for debugging',
      icon: `${window.location.origin}/hl-energy-logo-192w.png`,
      badge: `${window.location.origin}/hl-energy-logo-96w.png`,
      tag: 'sw-debug-test',
      requireInteraction: true,
      actions: [
        { action: 'view', title: 'View' },
        { action: 'dismiss', title: 'Dismiss' }
      ]
    })

    console.log('✅ [DEBUG] Service worker notification sent successfully!')

    // Show debug info in alert
    alert(`Service Worker Debug Info:

✅ Registration: Active
✅ State: ${registration.active?.state}
✅ Push Manager: Available
✅ Subscription: ${subscription ? 'Active' : 'None'}
✅ Endpoint: ${subscription?.endpoint.substring(0, 50)}...

Check browser console for detailed logs.`)

  } catch (err) {
    console.error('❌ [DEBUG] Service worker debug failed:', err)
    alert(`Service Worker Debug Failed: ${err.message}`)
  }
}

// Send notification to specific user
const sendToUser = async () => {
  try {
    isSendingToUser.value = true

    console.log('🔔 Sending notification to user:', targetUserId.value)

    // For now, use the custom endpoint which sends to the current user
    // TODO: Implement proper user targeting when backend is ready
    const response = await fetch('/api/v1/push/send/custom', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        title: `Personal Notification for User ${targetUserId.value}`,
        body: `This is a personal notification sent to user ${targetUserId.value}! 👤`
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    console.log('✅ User notification sent:', result)
    alert(`Notification sent to user ${targetUserId.value}!`)

  } catch (err) {
    console.error('❌ Failed to send user notification:', err)
    alert(`Failed to send notification: ${err.message}`)
  } finally {
    isSendingToUser.value = false
  }
}

// Send notification to admin users
const sendToAdmins = async () => {
  try {
    isSendingToAdmins.value = true

    const response = await fetch('/api/v1/push/send/admin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        title: 'Admin Alert',
        body: 'This is an admin-only notification! 👨‍💼'
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    console.log('✅ Admin notification sent:', result)
    alert(`Admin notification sent! Sent: ${result.data.sent}, Failed: ${result.data.failed}`)

  } catch (err) {
    console.error('❌ Failed to send admin notification:', err)
    alert(`Failed to send admin notification: ${err.message}`)
  } finally {
    isSendingToAdmins.value = false
  }
}

// Send system announcement
const sendSystemAnnouncement = async () => {
  try {
    isSendingAnnouncement.value = true

    const response = await fetch('/api/v1/push/send/announcement', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        title: 'System Update',
        body: 'This is a system-wide announcement for all users! 📢'
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    console.log('✅ System announcement sent:', result)
    alert(`System announcement sent! Sent: ${result.data.sent}, Failed: ${result.data.failed}`)

  } catch (err) {
    console.error('❌ Failed to send system announcement:', err)
    alert(`Failed to send system announcement: ${err.message}`)
  } finally {
    isSendingAnnouncement.value = false
  }
}

const toggleNotificationType = (typeId: string) => {
  const type = notificationTypes.value.find(t => t.id === typeId)
  if (type) {
    type.enabled = !type.enabled
    console.log(`Notification type ${typeId} ${type.enabled ? 'enabled' : 'disabled'}`)
    
    // Save preferences to localStorage
    const preferences = notificationTypes.value.reduce((acc, t) => {
      acc[t.id] = t.enabled
      return acc
    }, {} as Record<string, boolean>)
    
    localStorage.setItem('notification-preferences', JSON.stringify(preferences))
  }
}

// Load saved preferences
const loadPreferences = () => {
  try {
    const saved = localStorage.getItem('notification-preferences')
    if (saved) {
      const preferences = JSON.parse(saved)
      notificationTypes.value.forEach(type => {
        if (preferences.hasOwnProperty(type.id)) {
          type.enabled = preferences[type.id]
        }
      })
    }
  } catch (err) {
    console.error('Failed to load notification preferences:', err)
  }
}

// Initialize
loadPreferences()
</script>

<style scoped>
.glass-effect {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.checkbox:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.alert {
  border-radius: 0.75rem;
}
</style>
