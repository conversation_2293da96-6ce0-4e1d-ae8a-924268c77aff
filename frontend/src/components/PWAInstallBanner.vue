<template>
  <div v-if="shouldShowBanner" class="pwa-install-banner">
    <!-- Subtle Install Banner -->
    <div class="bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 border-b border-base-300/50">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-2 bg-primary/20 rounded-lg">
              <Icon name="smartphone" size="md" class="text-primary" />
            </div>
            <div>
              <h3 class="font-semibold text-base-content">Install HLenergy App</h3>
              <p class="text-sm text-base-content/70">Get faster access and work offline</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <button 
              @click="handleInstall" 
              class="btn btn-primary btn-sm"
              :disabled="isInstalling"
            >
              <Icon v-if="!isInstalling" name="smartphone" size="sm" class="mr-2" />
              <span v-if="!isInstalling">Install</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
            
            <button 
              @click="dismissBanner" 
              class="btn btn-ghost btn-sm btn-circle"
              title="Dismiss"
            >
              <Icon name="x" size="sm" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Install Button (Mobile) - Positioned to avoid WhatsApp button -->
    <div v-if="isMobile && !isInstalled" class="fixed bottom-6 right-20 z-40">
      <button 
        @click="handleInstall"
        class="btn btn-primary btn-circle btn-lg shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110"
        :disabled="isInstalling"
        title="Install HLenergy App"
      >
        <Icon v-if="!isInstalling" name="smartphone" size="lg" />
        <span v-else class="loading loading-spinner loading-md"></span>
      </button>
    </div>

    <!-- Install Success Toast -->
    <div v-if="showSuccessToast" class="toast toast-top toast-center z-50">
      <div class="card bg-gradient-to-br from-success/10 to-success/5 border border-success/20 shadow-2xl backdrop-blur-sm animate-fade-in-up">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-success/20 rounded-lg">
              <Icon name="check-circle" size="sm" class="text-success" />
            </div>
            <div class="flex-1">
              <div class="font-medium text-success">App installed successfully!</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { usePWA } from '@/composables/usePWA'
import Icon from '@/components/common/Icon.vue'

// Composables
const {
  shouldShowInstallPrompt,
  isInstalled,
  handleInstallPrompt,
  dismissInstallPrompt
} = usePWA()

// State
const isInstalling = ref(false)
const showSuccessToast = ref(false)
const bannerDismissed = ref(false)
const isMobile = ref(false)

// Computed
const shouldShowBanner = computed(() =>
  shouldShowInstallPrompt.value &&
  !isInstalled.value &&
  !bannerDismissed.value &&
  !isMobile.value // Only show banner on desktop
)

// Methods
const handleInstall = async () => {
  try {
    isInstalling.value = true
    const success = await handleInstallPrompt()
    
    if (success) {
      showSuccessToast.value = true
      bannerDismissed.value = true
      
      // Hide success toast after 3 seconds
      setTimeout(() => {
        showSuccessToast.value = false
      }, 3000)
    }
  } catch (error) {
    console.error('Install failed:', error)
  } finally {
    isInstalling.value = false
  }
}

const dismissBanner = () => {
  bannerDismissed.value = true
  dismissInstallPrompt()
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const handleResize = () => {
  checkMobile()
}

// Lifecycle
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
  
  // Check if banner was previously dismissed
  const dismissed = localStorage.getItem('pwa-banner-dismissed')
  if (dismissed) {
    const dismissedTime = parseInt(dismissed)
    const dayInMs = 24 * 60 * 60 * 1000
    // Show again after 7 days
    if ((Date.now() - dismissedTime) < (7 * dayInMs)) {
      bannerDismissed.value = true
    }
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// Watch for banner dismissal to save to localStorage
const saveDismissal = () => {
  if (bannerDismissed.value) {
    localStorage.setItem('pwa-banner-dismissed', Date.now().toString())
  }
}

// Auto-save dismissal state
const unwatchDismissal = computed(() => {
  if (bannerDismissed.value) {
    saveDismissal()
  }
  return bannerDismissed.value
})
</script>

<style scoped>
.pwa-install-banner {
  position: relative;
}

/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth animations */
.btn-circle {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Toast animations */
.toast {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .pwa-install-banner .btn {
    @apply btn-sm;
  }
  
  .fixed.bottom-6.right-20 {
    bottom: 5rem; /* Account for mobile navigation */
    right: 1rem; /* Closer to edge on mobile for better accessibility */
  }
}

/* Responsive banner */
@media (max-width: 640px) {
  .pwa-install-banner .flex {
    @apply flex-col space-y-3;
  }
  
  .pwa-install-banner .space-x-4 {
    @apply space-x-0 space-y-2 flex-col items-start;
  }
  
  .pwa-install-banner .space-x-3 {
    @apply space-x-0 space-y-2 flex-col w-full;
  }
  
  .pwa-install-banner .btn {
    @apply w-full;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pwa-install-banner {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .btn-circle {
    animation: none;
  }
  
  .toast {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}
</style>
