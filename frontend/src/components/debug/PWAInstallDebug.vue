<template>
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title">🔧 PWA Install Debug</h2>
      
      <!-- Status Display -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="stat bg-base-200 rounded-lg">
          <div class="stat-title">Authentication</div>
          <div class="stat-value text-sm" :class="authStore.isAuthenticated ? 'text-success' : 'text-error'">
            {{ authStore.isAuthenticated ? 'Authenticated' : 'Not Authenticated' }}
          </div>
        </div>
        
        <div class="stat bg-base-200 rounded-lg">
          <div class="stat-title">Install Status</div>
          <div class="stat-value text-sm" :class="isInstalled ? 'text-success' : 'text-warning'">
            {{ isInstalled ? 'Installed' : 'Not Installed' }}
          </div>
        </div>
        
        <div class="stat bg-base-200 rounded-lg">
          <div class="stat-title">Deferred Prompt</div>
          <div class="stat-value text-sm" :class="!!deferredPrompt ? 'text-success' : 'text-error'">
            {{ !!deferredPrompt ? 'Available' : 'Not Available' }}
          </div>
        </div>
        
        <div class="stat bg-base-200 rounded-lg">
          <div class="stat-title">Show Prompt</div>
          <div class="stat-value text-sm" :class="showInstallPrompt ? 'text-success' : 'text-error'">
            {{ showInstallPrompt ? 'Showing' : 'Hidden' }}
          </div>
        </div>
      </div>

      <!-- Detailed Info -->
      <div class="bg-base-200 p-4 rounded-lg mb-4">
        <h3 class="font-semibold mb-2">Detailed Status:</h3>
        <ul class="text-sm space-y-1">
          <li><strong>User:</strong> {{ authStore.user?.email || 'Not logged in' }}</li>
          <li><strong>User Role:</strong> {{ authStore.user?.role || 'N/A' }}</li>
          <li><strong>Recently Dismissed:</strong> {{ wasRecentlyDismissed() ? 'Yes' : 'No' }}</li>
          <li><strong>Should Show Prompt:</strong> {{ shouldShowInstallPrompt ? 'Yes' : 'No' }}</li>
          <li><strong>Is Standalone:</strong> {{ isStandalone ? 'Yes' : 'No' }}</li>
          <li><strong>Browser:</strong> {{ getBrowserInfo() }}</li>
        </ul>
      </div>

      <!-- Actions -->
      <div class="card-actions justify-end space-x-2">
        <button 
          class="btn btn-sm btn-outline"
          @click="checkConditions"
        >
          Check Conditions
        </button>
        
        <button 
          class="btn btn-sm btn-secondary"
          @click="clearDismissed"
        >
          Clear Dismissed
        </button>
        
        <button 
          class="btn btn-sm btn-warning"
          @click="forceShowInstallPrompt"
          :disabled="!deferredPrompt"
        >
          Force Show Prompt
        </button>
        
        <button 
          class="btn btn-sm btn-primary"
          @click="checkInstallPromptAfterLogin"
        >
          Trigger Login Check
        </button>
      </div>

      <!-- Console Output -->
      <div class="bg-base-300 p-3 rounded-lg mt-4">
        <h4 class="font-semibold mb-2">Console Output:</h4>
        <div class="text-xs font-mono max-h-32 overflow-y-auto">
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            <span class="text-gray-500">{{ log.time }}</span>
            <span :class="getLogClass(log.type)">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { usePWA } from '@/composables/usePWA'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const {
  showInstallPrompt,
  shouldShowInstallPrompt,
  isInstalled,
  isStandalone,
  deferredPrompt,
  forceShowInstallPrompt,
  checkInstallPromptAfterLogin
} = usePWA()

// Debug logging
const logs = ref<Array<{ time: string, type: string, message: string }>>([])

const addLog = (type: string, message: string) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  
  // Keep only last 20 logs
  if (logs.value.length > 20) {
    logs.value.shift()
  }
}

const getLogClass = (type: string) => {
  switch (type) {
    case 'error': return 'text-error'
    case 'warn': return 'text-warning'
    case 'success': return 'text-success'
    default: return 'text-base-content'
  }
}

// Helper functions
const wasRecentlyDismissed = () => {
  const dismissed = localStorage.getItem('pwa-install-dismissed')
  if (!dismissed) return false
  
  const dismissedTime = parseInt(dismissed)
  const dayInMs = 24 * 60 * 60 * 1000
  return (Date.now() - dismissedTime) < (7 * dayInMs)
}

const getBrowserInfo = () => {
  const ua = navigator.userAgent
  if (ua.includes('Chrome')) return 'Chrome'
  if (ua.includes('Firefox')) return 'Firefox'
  if (ua.includes('Safari')) return 'Safari'
  if (ua.includes('Edge')) return 'Edge'
  return 'Unknown'
}

const checkConditions = () => {
  addLog('info', 'Checking PWA install conditions...')
  addLog('info', `Authenticated: ${authStore.isAuthenticated}`)
  addLog('info', `Deferred Prompt: ${!!deferredPrompt.value}`)
  addLog('info', `Installed: ${isInstalled.value}`)
  addLog('info', `Recently Dismissed: ${wasRecentlyDismissed()}`)
  addLog('info', `Should Show: ${shouldShowInstallPrompt.value}`)
}

const clearDismissed = () => {
  localStorage.removeItem('pwa-install-dismissed')
  addLog('success', 'Cleared dismissed flag')
}

// Override console methods to capture logs
const originalConsoleLog = console.log
const originalConsoleWarn = console.warn
const originalConsoleError = console.error

onMounted(() => {
  // Capture PWA-related console logs
  console.log = (...args) => {
    const message = args.join(' ')
    if (message.includes('PWA') || message.includes('install') || message.includes('beforeinstallprompt')) {
      addLog('info', message)
    }
    originalConsoleLog.apply(console, args)
  }

  console.warn = (...args) => {
    const message = args.join(' ')
    if (message.includes('PWA') || message.includes('install')) {
      addLog('warn', message)
    }
    originalConsoleWarn.apply(console, args)
  }

  console.error = (...args) => {
    const message = args.join(' ')
    if (message.includes('PWA') || message.includes('install')) {
      addLog('error', message)
    }
    originalConsoleError.apply(console, args)
  }

  addLog('info', 'PWA Install Debug component mounted')
  checkConditions()
})

onUnmounted(() => {
  // Restore original console methods
  console.log = originalConsoleLog
  console.warn = originalConsoleWarn
  console.error = originalConsoleError
})
</script>
