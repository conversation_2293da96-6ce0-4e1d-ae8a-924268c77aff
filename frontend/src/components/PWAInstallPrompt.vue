<template>
  <div v-if="shouldShowInstallPrompt && !isInstalled" class="pwa-install-prompt">
    <!-- Mobile Bottom Sheet Style -->
    <div class="fixed inset-x-0 bottom-0 z-50 md:hidden">
      <div class="glass-effect bg-base-100/90 backdrop-blur-md border-t border-white/20 shadow-2xl p-4">
        <div class="flex items-center space-x-4">
          <div class="flex-shrink-0">
            <Logo size="md" :show-text="false" />
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-semibold text-base-content">Install HLenergy</h3>
            <p class="text-sm text-base-content opacity-70">Get the full app experience with offline access</p>
          </div>
          <div class="flex space-x-2">
            <button @click="dismissPrompt" class="btn btn-sm btn-ghost">
              Later
            </button>
            <button @click="installApp" class="btn btn-sm btn-primary">
              Install
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Desktop Banner Style -->
    <div class="hidden md:block fixed top-4 right-4 z-50">
      <div class="glass-effect shadow-2xl backdrop-blur-md border border-white/20 transition-all duration-300 hover:scale-[1.02] bg-gradient-to-br from-[#5cad64]/80 to-[#389868]/80 text-white rounded-lg p-4 max-w-sm">
        <div class="flex items-center space-x-3">
          <img src="/icon-192.svg" alt="HLenergy" class="w-8 h-8 rounded" />
          <div class="flex-1">
            <h3 class="font-semibold text-white">Install HLenergy App</h3>
            <p class="text-sm opacity-80 text-white">Add to your desktop for quick access</p>
          </div>
        </div>
        <div class="flex space-x-2 mt-3">
          <button @click="dismissPrompt" class="btn btn-sm btn-ghost text-white hover:bg-white/20 border-white/30">
            Not now
          </button>
          <button @click="installApp" class="btn btn-sm btn-primary bg-white/20 hover:bg-white/30 text-white border-white/30">
            Install
          </button>
        </div>
        <button @click="dismissPrompt" class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2 text-white hover:bg-white/20">
          ✕
        </button>
      </div>
    </div>

    <!-- Floating Action Button (Alternative) -->
    <div v-if="showFloatingButton" class="fixed bottom-6 right-6 z-40">
      <div class="tooltip tooltip-left" data-tip="Install HLenergy App">
        <button @click="installApp" class="btn btn-circle btn-primary btn-lg shadow-lg">
          <Icon name="smartphone" size="lg" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { usePWA } from '@/composables/usePWA'
import Logo from '@/components/common/Logo.vue'
import Icon from '@/components/common/Icon.vue'

const {
  shouldShowInstallPrompt,
  isInstalled,
  handleInstallPrompt,
  dismissInstallPrompt
} = usePWA()

// Local state
const showFloatingButton = ref(false)
const installAttempted = ref(false)

// Computed
const shouldShowPrompt = computed(() => {
  return shouldShowInstallPrompt.value && !isInstalled.value && !installAttempted.value
})

// Methods
const installApp = async () => {
  try {
    installAttempted.value = true
    const success = await handleInstallPrompt()
    
    if (success) {
      console.log('App installation successful')
      // Show success message
      showSuccessMessage()
    } else {
      console.log('App installation cancelled by user')
      installAttempted.value = false
    }
  } catch (error) {
    console.error('Error during app installation:', error)
    installAttempted.value = false
    showErrorMessage()
  }
}

const dismissPrompt = () => {
  dismissInstallPrompt()
  // Show floating button after dismissing main prompt
  setTimeout(() => {
    showFloatingButton.value = true
  }, 5000) // Show floating button after 5 seconds
}

const showSuccessMessage = () => {
  // You can integrate with a toast/notification system here
  console.log('PWA installed successfully!')
}

const showErrorMessage = () => {
  // You can integrate with a toast/notification system here
  console.log('Failed to install PWA')
}

// Auto-show floating button after some time if prompt was dismissed
let floatingButtonTimer: number

onMounted(() => {
  // Show floating button after 30 seconds if prompt not shown
  floatingButtonTimer = window.setTimeout(() => {
    if (!shouldShowInstallPrompt.value && !isInstalled.value) {
      showFloatingButton.value = true
    }
  }, 30000)
})

onUnmounted(() => {
  if (floatingButtonTimer) {
    clearTimeout(floatingButtonTimer)
  }
})
</script>

<style scoped>
.pwa-install-prompt {
  /* Ensure proper z-index layering */
  z-index: 1000;
}

/* Proper glass effect */
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-effect:hover::after {
  opacity: 1;
}

.glass-effect > div {
  position: relative;
  z-index: 3;
}

/* Enhanced hover effects */
.glass-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

/* Dark theme adjustments */
[data-theme="hlenergy-dark"] .glass-effect::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.03) 100%
  );
}

[data-theme="hlenergy-dark"] .glass-effect:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}

/* Smooth animations */
.alert {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile bottom sheet animation */
.fixed.inset-x-0.bottom-0 > div {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Floating button animation */
.btn-circle {
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Hover effects */
.btn-circle:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .alert {
    max-width: calc(100vw - 2rem);
    margin: 0 1rem;
  }
}
</style>
