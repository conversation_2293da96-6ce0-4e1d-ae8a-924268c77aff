/**
 * Global reCAPTCHA cleanup utility
 * Ensures complete removal of all reCAPTCHA elements from the DOM
 */

export interface RecaptchaCleanupResult {
  success: boolean
  elementsRemoved: number
  details: string[]
}

/**
 * Performs comprehensive reCAPTCHA cleanup
 * Removes all reCAPTCHA related elements, scripts, and global variables
 */
export function cleanupRecaptcha(): RecaptchaCleanupResult {
  const details: string[] = []
  let elementsRemoved = 0

  try {
    console.log('🧹 Starting comprehensive reCAPTCHA cleanup...')

    // 1. Remove reCAPTCHA badge (the persistent bottom-right element)
    const badges = document.querySelectorAll('.grecaptcha-badge')
    badges.forEach(badge => {
      badge.remove()
      elementsRemoved++
      details.push('Removed reCAPTCHA badge')
    })

    // 2. Remove reCAPTCHA script
    const scripts = document.querySelectorAll('script[src*="recaptcha"], script#recaptcha-v3-script')
    scripts.forEach(script => {
      script.remove()
      elementsRemoved++
      details.push('Removed reCAPTCHA script')
    })

    // 3. Remove reCAPTCHA iframes
    const iframes = document.querySelectorAll('iframe[src*="recaptcha"]')
    iframes.forEach(iframe => {
      iframe.remove()
      elementsRemoved++
      details.push('Removed reCAPTCHA iframe')
    })

    // 4. Remove elements with reCAPTCHA IDs or classes
    const recaptchaElements = document.querySelectorAll(
      '[id*="grecaptcha"], [class*="grecaptcha"], [id*="recaptcha"], [class*="recaptcha"]'
    )
    recaptchaElements.forEach(element => {
      // Don't remove our own component
      if (!element.closest('.recaptcha-v3')) {
        element.remove()
        elementsRemoved++
        details.push(`Removed element: ${element.tagName}`)
      }
    })

    // 5. Remove reCAPTCHA configuration elements
    const configElements = document.querySelectorAll('[id^="___grecaptcha_cfg"]')
    configElements.forEach(element => {
      element.remove()
      elementsRemoved++
      details.push('Removed reCAPTCHA config element')
    })

    // 6. Clean up global variables
    if (typeof window !== 'undefined') {
      // Remove grecaptcha global
      if (window.grecaptcha) {
        delete window.grecaptcha
        details.push('Cleaned up window.grecaptcha')
      }

      // Remove other reCAPTCHA related globals
      Object.keys(window).forEach(key => {
        if (key.includes('grecaptcha') || key.includes('recaptcha')) {
          try {
            delete (window as any)[key]
            details.push(`Cleaned up window.${key}`)
          } catch (e) {
            // Some properties might not be deletable
            details.push(`Could not delete window.${key} (read-only)`)
          }
        }
      })

      // Reset instance counter
      if (window.__recaptchaInstances) {
        window.__recaptchaInstances = 0
        details.push('Reset reCAPTCHA instance counter')
      }
    }

    // 7. Remove any remaining reCAPTCHA styles and positioning CSS
    const styles = document.querySelectorAll('style[id*="recaptcha"], link[href*="recaptcha"], #recaptcha-positioning-css')
    styles.forEach(style => {
      style.remove()
      elementsRemoved++
      details.push('Removed reCAPTCHA style')
    })

    console.log(`🧹 reCAPTCHA cleanup completed: ${elementsRemoved} elements removed`)
    details.forEach(detail => console.log(`  - ${detail}`))

    return {
      success: true,
      elementsRemoved,
      details
    }

  } catch (error) {
    console.error('❌ reCAPTCHA cleanup failed:', error)
    details.push(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    
    return {
      success: false,
      elementsRemoved,
      details
    }
  }
}

/**
 * Checks if reCAPTCHA elements are present in the DOM
 */
export function hasRecaptchaElements(): boolean {
  const selectors = [
    '.grecaptcha-badge',
    'script[src*="recaptcha"]',
    'iframe[src*="recaptcha"]',
    '[id*="grecaptcha"]',
    '[class*="grecaptcha"]'
  ]

  return selectors.some(selector => document.querySelector(selector) !== null)
}

/**
 * Auto-cleanup when navigating away from pages with reCAPTCHA
 * Can be called in router guards or component lifecycle hooks
 */
export function autoCleanupOnNavigation(): void {
  // Only cleanup if reCAPTCHA elements exist and no instances are active
  if (hasRecaptchaElements() && (!window.__recaptchaInstances || window.__recaptchaInstances <= 0)) {
    console.log('🧹 Auto-cleaning reCAPTCHA on navigation')
    cleanupRecaptcha()
  }
}

/**
 * Development helper to manually trigger cleanup
 * Available in browser console as window.cleanupRecaptcha()
 */
if (import.meta.env.DEV && typeof window !== 'undefined') {
  (window as any).cleanupRecaptcha = cleanupRecaptcha
  (window as any).hasRecaptchaElements = hasRecaptchaElements
  console.log('🔧 reCAPTCHA cleanup utilities available:')
  console.log('  - window.cleanupRecaptcha()')
  console.log('  - window.hasRecaptchaElements()')
}

// Export default cleanup function
export default cleanupRecaptcha
