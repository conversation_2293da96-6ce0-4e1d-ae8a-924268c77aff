// Standalone Activity Tracker - No Vue dependencies
// Can be used outside of Vue component context

class ActivityTracker {
  private isActive = false
  private lastActivity = Date.now()
  private idleTimeout = 15 * 60 * 1000 // 15 minutes
  private checkInterval: number | null = null
  private eventListeners: Array<{ event: string; handler: EventListener }> = []
  private authStore: any = null
  private lastAuthUpdate = 0
  private readonly AUTH_THROTTLE_MS = 30000 // Update auth store every 30 seconds max

  // Events that indicate user activity
  private readonly activityEvents = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ]

  constructor(idleTimeoutMs = 15 * 60 * 1000) {
    this.idleTimeout = idleTimeoutMs
  }

  // Handle user activity
  private handleActivity = () => {
    const now = Date.now()
    this.lastActivity = now

    // Throttle activity updates to prevent excessive calls
    if (!this.isActive) {
      this.isActive = true
      console.log('🟢 User activity detected')
    }

    // Update auth store if available and throttled
    if (this.authStore && now - this.lastAuthUpdate > this.AUTH_THROTTLE_MS) {
      this.lastAuthUpdate = now
      try {
        if (this.authStore.isAuthenticated && !this.authStore.isLocked) {
          this.authStore.updateActivity()
        }
      } catch (error) {
        console.warn('Failed to update auth store activity:', error)
      }
    }
  }

  // Check for idle state
  private checkIdleState = () => {
    const now = Date.now()
    const timeSinceLastActivity = now - this.lastActivity
    
    if (timeSinceLastActivity >= this.idleTimeout && this.isActive) {
      this.isActive = false
      console.log('🔴 User idle detected')
      
      // Dispatch custom event for idle state
      window.dispatchEvent(new CustomEvent('user-idle', {
        detail: { idleTime: timeSinceLastActivity }
      }))
    }
  }

  // Start tracking user activity
  startTracking() {
    if (this.checkInterval) {
      console.warn('Activity tracker already running')
      return
    }

    console.log('🔍 Starting activity tracking')
    
    // Add event listeners
    this.activityEvents.forEach(event => {
      const handler = this.handleActivity
      document.addEventListener(event, handler, { passive: true })
      this.eventListeners.push({ event, handler })
    })

    // Start checking for idle state every 30 seconds
    this.checkInterval = window.setInterval(this.checkIdleState, 30000)
    
    // Initial activity
    this.handleActivity()
  }

  // Stop tracking
  stopTracking() {
    console.log('🛑 Stopping activity tracking')
    
    // Remove event listeners
    this.eventListeners.forEach(({ event, handler }) => {
      document.removeEventListener(event, handler)
    })
    this.eventListeners = []

    // Clear interval
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  // Get current activity state
  getActivityState() {
    return {
      isActive: this.isActive,
      lastActivity: this.lastActivity,
      timeSinceLastActivity: Date.now() - this.lastActivity
    }
  }

  // Check if user is currently idle
  isUserIdle() {
    return Date.now() - this.lastActivity >= this.idleTimeout
  }

  // Update idle timeout
  setIdleTimeout(timeoutMs: number) {
    this.idleTimeout = timeoutMs
    console.log(`🕐 Idle timeout updated to ${timeoutMs / 1000 / 60} minutes`)
  }

  // Set auth store for activity updates
  setAuthStore(authStore: any) {
    this.authStore = authStore
    console.log('🔐 Auth store connected to activity tracker')
  }
}

// Create global instance
const globalActivityTracker = new ActivityTracker()

// Auto-start when imported (safe for lazy loading)
if (typeof window !== 'undefined') {
  globalActivityTracker.startTracking()
  
  // Add to window for debugging
  if (import.meta.env.DEV) {
    ;(window as any).activityTracker = globalActivityTracker
  }
  
  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    globalActivityTracker.stopTracking()
  })
}

export default globalActivityTracker
export { ActivityTracker }
