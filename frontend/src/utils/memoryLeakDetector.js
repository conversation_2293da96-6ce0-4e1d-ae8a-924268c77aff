// Memory Leak Detection Utility
// Helps identify and prevent memory leaks in the application

class MemoryLeakDetector {
  constructor() {
    this.intervals = new Set()
    this.timeouts = new Set()
    this.eventListeners = new Map()
    this.observers = new Set()
    this.isMonitoring = false
    this.memorySnapshots = []
    this.startTime = Date.now()
  }

  // Override native functions to track them
  startMonitoring() {
    if (this.isMonitoring) return
    this.isMonitoring = true

    // Track setInterval
    const originalSetInterval = window.setInterval
    window.setInterval = (...args) => {
      const id = originalSetInterval.apply(window, args)
      this.intervals.add(id)
      console.log(`🔍 [Memory] setInterval created: ${id} (${args[1]}ms)`)
      return id
    }

    // Track setTimeout
    const originalSetTimeout = window.setTimeout
    window.setTimeout = (...args) => {
      const id = originalSetTimeout.apply(window, args)
      this.timeouts.add(id)
      console.log(`🔍 [Memory] setTimeout created: ${id} (${args[1]}ms)`)
      return id
    }

    // Track clearInterval
    const originalClearInterval = window.clearInterval
    window.clearInterval = (id) => {
      originalClearInterval.call(window, id)
      if (this.intervals.has(id)) {
        this.intervals.delete(id)
        console.log(`✅ [Memory] setInterval cleared: ${id}`)
      }
    }

    // Track clearTimeout
    const originalClearTimeout = window.clearTimeout
    window.clearTimeout = (id) => {
      originalClearTimeout.call(window, id)
      if (this.timeouts.has(id)) {
        this.timeouts.delete(id)
        console.log(`✅ [Memory] setTimeout cleared: ${id}`)
      }
    }

    // Track addEventListener
    const originalAddEventListener = EventTarget.prototype.addEventListener
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      originalAddEventListener.call(this, type, listener, options)
      
      if (!this.memoryLeakDetectorListeners) {
        this.memoryLeakDetectorListeners = new Set()
      }
      this.memoryLeakDetectorListeners.add({ type, listener, options })
      
      console.log(`🔍 [Memory] Event listener added: ${type} on`, this.constructor.name)
    }

    console.log('🔍 Memory leak detection started')
    this.startMemoryMonitoring()
  }

  stopMonitoring() {
    this.isMonitoring = false
    console.log('🔍 Memory leak detection stopped')
  }

  // Monitor memory usage
  startMemoryMonitoring() {
    const monitorInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(monitorInterval)
        return
      }

      const memInfo = this.getMemoryInfo()
      this.memorySnapshots.push({
        timestamp: Date.now(),
        ...memInfo
      })

      // Keep only last 20 snapshots
      if (this.memorySnapshots.length > 20) {
        this.memorySnapshots.shift()
      }

      // Check for memory leaks
      this.checkForLeaks()
    }, 10000) // Every 10 seconds
  }

  getMemoryInfo() {
    const info = {
      activeIntervals: this.intervals.size,
      activeTimeouts: this.timeouts.size,
      eventListeners: this.eventListeners.size
    }

    // Add performance memory if available
    if (performance.memory) {
      info.usedJSHeapSize = performance.memory.usedJSHeapSize
      info.totalJSHeapSize = performance.memory.totalJSHeapSize
      info.jsHeapSizeLimit = performance.memory.jsHeapSizeLimit
    }

    return info
  }

  checkForLeaks() {
    const current = this.memorySnapshots[this.memorySnapshots.length - 1]
    if (!current) return

    // Check for too many active intervals
    if (current.activeIntervals > 10) {
      console.warn(`⚠️ [Memory] High number of active intervals: ${current.activeIntervals}`)
      this.logActiveIntervals()
    }

    // Check for memory growth
    if (this.memorySnapshots.length >= 5) {
      const oldest = this.memorySnapshots[this.memorySnapshots.length - 5]
      if (current.usedJSHeapSize && oldest.usedJSHeapSize) {
        const growth = current.usedJSHeapSize - oldest.usedJSHeapSize
        const growthMB = growth / (1024 * 1024)
        
        if (growthMB > 10) { // More than 10MB growth
          console.warn(`⚠️ [Memory] Significant memory growth: ${growthMB.toFixed(2)}MB in last 50 seconds`)
        }
      }
    }
  }

  logActiveIntervals() {
    console.log('🔍 Active intervals:', Array.from(this.intervals))
  }

  logActiveTimeouts() {
    console.log('🔍 Active timeouts:', Array.from(this.timeouts))
  }

  // Get memory report
  getMemoryReport() {
    const current = this.getMemoryInfo()
    const runtime = (Date.now() - this.startTime) / 1000 / 60 // minutes

    return {
      runtime: `${runtime.toFixed(1)} minutes`,
      activeIntervals: current.activeIntervals,
      activeTimeouts: current.activeTimeouts,
      eventListeners: current.eventListeners,
      memoryUsage: current.usedJSHeapSize ? 
        `${(current.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB` : 'N/A',
      memoryLimit: current.jsHeapSizeLimit ? 
        `${(current.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB` : 'N/A'
    }
  }

  // Force cleanup of all tracked resources
  forceCleanup() {
    console.log('🧹 [Memory] Force cleanup started...')
    
    // Clear all intervals
    this.intervals.forEach(id => {
      clearInterval(id)
      console.log(`🧹 Cleared interval: ${id}`)
    })
    this.intervals.clear()

    // Clear all timeouts
    this.timeouts.forEach(id => {
      clearTimeout(id)
      console.log(`🧹 Cleared timeout: ${id}`)
    })
    this.timeouts.clear()

    console.log('🧹 [Memory] Force cleanup completed')
  }
}

// Create global instance
const memoryLeakDetector = new MemoryLeakDetector()

// Manual start only - to prevent setTimeout overhead
if (import.meta.env.DEV) {
  // Add to window for manual access
  window.memoryLeakDetector = memoryLeakDetector

  console.log('🔍 Memory leak detector available at window.memoryLeakDetector')
  console.log('🔍 Call memoryLeakDetector.startMonitoring() to enable monitoring')
  console.log('🔍 Use memoryLeakDetector.getMemoryReport() to check status')
  console.log('🔍 Use memoryLeakDetector.forceCleanup() to clear all timers')
}

export default memoryLeakDetector
