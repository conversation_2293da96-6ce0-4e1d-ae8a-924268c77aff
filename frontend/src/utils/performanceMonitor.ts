/**
 * Performance monitoring utility to track CPU usage, memory consumption,
 * and identify performance bottlenecks
 */

interface PerformanceMetrics {
  timestamp: number
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  activeIntervals: number
  activeTimeouts: number
  socketConnections: number
  domNodes: number
  eventListeners: number
}

interface IntervalInfo {
  id: number
  callback: string
  delay: number
  createdAt: number
  lastRun: number
  runCount: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private intervals = new Map<number, IntervalInfo>()
  private timeouts = new Map<number, any>()
  private isMonitoring = false
  private monitoringInterval?: number
  private originalSetInterval: typeof setInterval
  private originalSetTimeout: typeof setTimeout
  private originalClearInterval: typeof clearInterval
  private originalClearTimeout: typeof clearTimeout

  constructor() {
    try {
      // Bind the original functions to maintain proper context
      this.originalSetInterval = window.setInterval.bind(window)
      this.originalSetTimeout = window.setTimeout.bind(window)
      this.originalClearInterval = window.clearInterval.bind(window)
      this.originalClearTimeout = window.clearTimeout.bind(window)

      this.setupInterceptors()
    } catch (error) {
      console.warn('📊 Performance monitor initialization failed:', error)
      // Fallback to original functions without monitoring
      this.originalSetInterval = window.setInterval.bind(window)
      this.originalSetTimeout = window.setTimeout.bind(window)
      this.originalClearInterval = window.clearInterval.bind(window)
      this.originalClearTimeout = window.clearTimeout.bind(window)
    }
  }

  /**
   * Intercept setInterval/setTimeout to track active timers
   */
  private setupInterceptors() {
    try {
      const self = this

      // Intercept setInterval
      window.setInterval = function(callback: any, delay: number, ...args: any[]) {
        try {
          const id = self.originalSetInterval.call(window, callback, delay, ...args)

          self.intervals.set(id, {
            id,
            callback: callback.toString().substring(0, 100),
            delay,
            createdAt: Date.now(),
            lastRun: Date.now(),
            runCount: 0
          })

          console.log(`🔄 New interval created: ID ${id}, delay: ${delay}ms`)
          return id
        } catch (error) {
          console.warn('📊 Error in setInterval interceptor:', error)
          return self.originalSetInterval.call(window, callback, delay, ...args)
        }
      }

      // Intercept setTimeout
      window.setTimeout = function(callback: any, delay?: number, ...args: any[]) {
        try {
          const id = self.originalSetTimeout.call(window, callback, delay, ...args)

          self.timeouts.set(id, {
            id,
            callback: callback.toString().substring(0, 100),
            delay: delay || 0,
            createdAt: Date.now()
          })

          return id
        } catch (error) {
          console.warn('📊 Error in setTimeout interceptor:', error)
          return self.originalSetTimeout.call(window, callback, delay, ...args)
        }
      }

      // Intercept clearInterval
      window.clearInterval = function(id?: number) {
        try {
          if (id !== undefined) {
            self.intervals.delete(id)
            console.log(`🛑 Interval cleared: ID ${id}`)
          }
          return self.originalClearInterval.call(window, id)
        } catch (error) {
          console.warn('📊 Error in clearInterval interceptor:', error)
          return self.originalClearInterval.call(window, id)
        }
      }

      // Intercept clearTimeout
      window.clearTimeout = function(id?: number) {
        try {
          if (id !== undefined) {
            self.timeouts.delete(id)
          }
          return self.originalClearTimeout.call(window, id)
        } catch (error) {
          console.warn('📊 Error in clearTimeout interceptor:', error)
          return self.originalClearTimeout.call(window, id)
        }
      }
    } catch (error) {
      console.warn('📊 Failed to setup performance interceptors:', error)
    }
  }

  /**
   * Start monitoring performance
   */
  startMonitoring(intervalMs: number = 5000) {
    if (this.isMonitoring) return

    try {
      this.isMonitoring = true
      console.log('📊 Performance monitoring started')

      this.monitoringInterval = this.originalSetInterval(() => {
        try {
          this.collectMetrics()
        } catch (error) {
          console.warn('📊 Error collecting performance metrics:', error)
        }
      }, intervalMs)
    } catch (error) {
      console.error('📊 Failed to start performance monitoring:', error)
      this.isMonitoring = false
    }
  }

  /**
   * Stop monitoring performance
   */
  stopMonitoring() {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    if (this.monitoringInterval) {
      this.originalClearInterval(this.monitoringInterval)
      this.monitoringInterval = undefined
    }
    console.log('📊 Performance monitoring stopped')
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics() {
    const memory = (performance as any).memory
    const memoryUsage = memory ? {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
    } : { used: 0, total: 0, percentage: 0 }

    const metrics: PerformanceMetrics = {
      timestamp: Date.now(),
      memoryUsage,
      activeIntervals: this.intervals.size,
      activeTimeouts: this.timeouts.size,
      socketConnections: this.getSocketConnectionCount(),
      domNodes: document.querySelectorAll('*').length,
      eventListeners: this.getEventListenerCount()
    }

    this.metrics.push(metrics)

    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics.shift()
    }

    // Log warnings for high resource usage
    this.checkForWarnings(metrics)
  }

  /**
   * Check for performance warnings
   */
  private checkForWarnings(metrics: PerformanceMetrics) {
    if (metrics.activeIntervals > 10) {
      console.warn(`⚠️ High number of active intervals: ${metrics.activeIntervals}`)
      console.warn('🔍 Active intervals:', this.getActiveIntervals())
    }

    if (metrics.memoryUsage.percentage > 90) { // Raised threshold from 80 to 90
      const usedMB = Math.round(metrics.memoryUsage.used / 1024 / 1024)
      const totalMB = Math.round(metrics.memoryUsage.total / 1024 / 1024)
      console.warn(`⚠️ High memory usage: ${metrics.memoryUsage.percentage}% (${usedMB}MB / ${totalMB}MB)`)

      // Check for potential memory leaks
      this.checkForMemoryLeaks()
    }

    if (metrics.domNodes > 5000) {
      console.warn(`⚠️ High DOM node count: ${metrics.domNodes}`)
    }

    if (metrics.activeTimeouts > 50) {
      console.warn(`⚠️ High number of active timeouts: ${metrics.activeTimeouts}`)
    }
  }

  /**
   * Check for potential memory leaks
   */
  private checkForMemoryLeaks() {
    const longRunningIntervals = Array.from(this.intervals.values())
      .filter(interval => Date.now() - interval.createdAt > 5 * 60 * 1000) // 5 minutes
      .filter(interval => interval.runCount > 100) // High run count

    if (longRunningIntervals.length > 0) {
      console.warn('🚨 Potential memory leak detected - long-running intervals:')
      longRunningIntervals.forEach(interval => {
        console.warn(`  - ID ${interval.id}: running for ${Math.round((Date.now() - interval.createdAt) / 1000)}s, ${interval.runCount} runs`)
      })
    }

    // Check for excessive event listeners (if available)
    if (typeof window !== 'undefined' && (window as any).getEventListeners) {
      const listeners = (window as any).getEventListeners(document)
      const totalListeners = Object.values(listeners).reduce((sum: number, arr: any) => sum + arr.length, 0)

      if (totalListeners > 100) {
        console.warn(`🚨 High number of event listeners detected: ${totalListeners}`)
      }
    }
  }

  /**
   * Get Socket.io connection count
   */
  private getSocketConnectionCount(): number {
    // Try to access global socket instances
    const globalSocket = (window as any).globalSocket
    if (globalSocket?.socket?.value?.connected) {
      return 1
    }
    return 0
  }

  /**
   * Estimate event listener count
   */
  private getEventListenerCount(): number {
    // This is an approximation - exact count is not easily accessible
    return document.querySelectorAll('[onclick], [onload], [onchange]').length
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  /**
   * Get active intervals info
   */
  getActiveIntervals(): IntervalInfo[] {
    return Array.from(this.intervals.values())
  }

  /**
   * Get active timeouts info
   */
  getActiveTimeouts(): any[] {
    return Array.from(this.timeouts.values())
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const latest = this.metrics[this.metrics.length - 1]
    if (!latest) return null

    return {
      memoryUsage: `${latest.memoryUsage.percentage}% (${Math.round(latest.memoryUsage.used / 1024 / 1024)}MB)`,
      activeIntervals: latest.activeIntervals,
      activeTimeouts: latest.activeTimeouts,
      socketConnections: latest.socketConnections,
      domNodes: latest.domNodes,
      intervalDetails: this.getActiveIntervals().map(interval => ({
        id: interval.id,
        delay: interval.delay,
        runCount: interval.runCount,
        age: Math.round((Date.now() - interval.createdAt) / 1000)
      }))
    }
  }

  /**
   * Get current monitoring status
   */
  get isMonitoringActive() {
    return this.isMonitoring
  }

  /**
   * Get monitoring status details
   */
  getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      metricsCount: this.metrics.length,
      activeIntervals: this.intervals.size,
      activeTimeouts: this.timeouts.size
    }
  }

  /**
   * Clear all tracked intervals (emergency cleanup)
   */
  clearAllIntervals() {
    console.warn('🚨 Emergency cleanup: clearing all tracked intervals')
    for (const id of this.intervals.keys()) {
      this.originalClearInterval(id)
    }
    this.intervals.clear()
  }

  /**
   * Clear all tracked timeouts
   */
  clearAllTimeouts() {
    console.warn('🚨 Emergency cleanup: clearing all tracked timeouts')
    for (const id of this.timeouts.keys()) {
      this.originalClearTimeout(id)
    }
    this.timeouts.clear()
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage() {
    const memory = (performance as any).memory
    return memory ? {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
    } : { used: 0, total: 0, percentage: 0 }
  }

  /**
   * Get current performance metrics
   */
  private getPerformanceMetrics() {
    const latest = this.metrics[this.metrics.length - 1]
    return latest || {
      timestamp: Date.now(),
      memoryUsage: this.getMemoryUsage(),
      activeIntervals: this.intervals.size,
      activeTimeouts: this.timeouts.size,
      socketConnections: 0,
      domNodes: document.querySelectorAll('*').length,
      eventListeners: 0
    }
  }

  /**
   * Get enhanced memory report with leak detection
   */
  getMemoryReport() {
    const memoryInfo = this.getMemoryUsage()
    const longRunningIntervals = Array.from(this.intervals.values())
      .filter(interval => Date.now() - interval.createdAt > 5 * 60 * 1000) // 5+ minutes

    const suspiciousTimeouts = Array.from(this.timeouts.values())
      .filter(timeout => Date.now() - timeout.createdAt > 2 * 60 * 1000) // 2+ minutes

    const report = {
      intervals: this.intervals.size,
      timeouts: this.timeouts.size,
      longRunningIntervals: longRunningIntervals.length,
      suspiciousTimeouts: suspiciousTimeouts.length,
      memory: memoryInfo,
      performance: this.getPerformanceMetrics(),
      memoryLeakWarnings: this.getMemoryLeakWarnings(),
      recommendations: this.getOptimizationRecommendations()
    }

    console.log('📊 Enhanced Memory Report:', report)

    // Show warnings if issues detected
    if (longRunningIntervals.length > 0) {
      console.warn(`🚨 ${longRunningIntervals.length} long-running intervals detected`)
    }
    if (suspiciousTimeouts.length > 0) {
      console.warn(`🚨 ${suspiciousTimeouts.length} suspicious timeouts detected`)
    }

    return report
  }

  /**
   * Get memory leak warnings
   */
  private getMemoryLeakWarnings(): string[] {
    const warnings = []

    if (this.intervals.size > 20) {
      warnings.push(`High number of active intervals: ${this.intervals.size}`)
    }

    if (this.timeouts.size > 50) {
      warnings.push(`High number of active timeouts: ${this.timeouts.size}`)
    }

    const memoryInfo = this.getMemoryUsage()
    if (memoryInfo.percentage > 85) {
      warnings.push(`High memory usage: ${memoryInfo.percentage}%`)
    }

    return warnings
  }

  /**
   * Get optimization recommendations
   */
  private getOptimizationRecommendations(): string[] {
    const recommendations = []
    const memoryInfo = this.getMemoryUsage()

    if (this.intervals.size > 10) {
      recommendations.push('Consider consolidating intervals or using a single interval with multiple tasks')
    }

    if (memoryInfo.percentage > 80) {
      recommendations.push('Consider implementing data pagination or lazy loading')
      recommendations.push('Review reactive objects for unnecessary data retention')
    }

    if (this.intervals.size > 0) {
      recommendations.push('Ensure all intervals are properly cleaned up in component onUnmounted hooks')
    }

    return recommendations
  }
}

// Create global instance
export const performanceMonitor = new PerformanceMonitor()

// Auto-start monitoring in development
if (import.meta.env.DEV) {
  try {
    performanceMonitor.startMonitoring()
  } catch (error) {
    console.warn('📊 Failed to auto-start performance monitoring:', error)
  }
}

// Expose to window for debugging
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = performanceMonitor
}

export default performanceMonitor
