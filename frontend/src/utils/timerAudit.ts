/**
 * Timer Usage Audit Tool
 * 
 * Analyzes and reports on all active intervals and timeouts in the application
 * to identify potential memory leaks and optimization opportunities.
 */

interface TimerInfo {
  id: number
  type: 'interval' | 'timeout'
  delay: number
  callback: string
  createdAt: number
  source?: string
  runCount?: number
}

class TimerAudit {
  private timers = new Map<number, TimerInfo>()
  private originalSetInterval: typeof setInterval
  private originalSetTimeout: typeof setTimeout
  private originalClearInterval: typeof clearInterval
  private originalClearTimeout: typeof clearTimeout
  private isMonitoring = false

  constructor() {
    // Store original functions
    this.originalSetInterval = window.setInterval.bind(window)
    this.originalSetTimeout = window.setTimeout.bind(window)
    this.originalClearInterval = window.clearInterval.bind(window)
    this.originalClearTimeout = window.clearTimeout.bind(window)
  }

  startMonitoring() {
    if (this.isMonitoring) return
    this.isMonitoring = true

    const self = this

    // Intercept setInterval
    window.setInterval = function(callback: any, delay: number, ...args: any[]) {
      const id = self.originalSetInterval.call(window, callback, delay, ...args)
      
      // Try to identify the source
      const stack = new Error().stack || ''
      const source = self.identifySource(stack)
      
      self.timers.set(id, {
        id,
        type: 'interval',
        delay,
        callback: callback.toString().substring(0, 100),
        createdAt: Date.now(),
        source,
        runCount: 0
      })

      console.log(`🔄 [AUDIT] Interval created: ID ${id}, delay: ${delay}ms, source: ${source}`)
      return id
    }

    // Intercept setTimeout
    window.setTimeout = function(callback: any, delay: number, ...args: any[]) {
      const id = self.originalSetTimeout.call(window, callback, delay, ...args)
      
      const stack = new Error().stack || ''
      const source = self.identifySource(stack)
      
      self.timers.set(id, {
        id,
        type: 'timeout',
        delay,
        callback: callback.toString().substring(0, 100),
        createdAt: Date.now(),
        source
      })

      console.log(`⏰ [AUDIT] Timeout created: ID ${id}, delay: ${delay}ms, source: ${source}`)
      return id
    }

    // Intercept clearInterval
    window.clearInterval = function(id: number) {
      if (self.timers.has(id)) {
        const timer = self.timers.get(id)!
        console.log(`🛑 [AUDIT] Interval cleared: ID ${id}, source: ${timer.source}`)
        self.timers.delete(id)
      }
      return self.originalClearInterval.call(window, id)
    }

    // Intercept clearTimeout
    window.clearTimeout = function(id: number) {
      if (self.timers.has(id)) {
        const timer = self.timers.get(id)!
        console.log(`🛑 [AUDIT] Timeout cleared: ID ${id}, source: ${timer.source}`)
        self.timers.delete(id)
      }
      return self.originalClearTimeout.call(window, id)
    }

    console.log('🔍 Timer audit monitoring started')
  }

  private identifySource(stack: string): string {
    const lines = stack.split('\n')
    
    // Look for recognizable patterns in the stack trace
    for (const line of lines) {
      if (line.includes('useSocket')) return 'Socket.io'
      if (line.includes('useBackgroundTasks')) return 'Background Tasks'
      if (line.includes('AdminDashboard')) return 'Admin Dashboard'
      if (line.includes('EmailWorkerControl')) return 'Email Worker'
      if (line.includes('SocketMonitor')) return 'Socket Monitor'
      if (line.includes('activityTracker')) return 'Activity Tracker'
      if (line.includes('sessionService')) return 'Session Service'
      if (line.includes('performanceMonitor')) return 'Performance Monitor'
      if (line.includes('memoryLeakDetector')) return 'Memory Leak Detector'
      if (line.includes('PWA')) return 'PWA Service'
      if (line.includes('notification')) return 'Notifications'
    }

    return 'Unknown'
  }

  getReport() {
    const now = Date.now()
    const intervals = Array.from(this.timers.values()).filter(t => t.type === 'interval')
    const timeouts = Array.from(this.timers.values()).filter(t => t.type === 'timeout')

    // Group by source
    const bySource = new Map<string, TimerInfo[]>()
    this.timers.forEach(timer => {
      const source = timer.source || 'Unknown'
      if (!bySource.has(source)) {
        bySource.set(source, [])
      }
      bySource.get(source)!.push(timer)
    })

    // Find long-running timers
    const longRunning = intervals.filter(t => now - t.createdAt > 5 * 60 * 1000) // 5+ minutes

    // Find high-frequency timers
    const highFrequency = intervals.filter(t => t.delay < 5000) // Less than 5 seconds

    const report = {
      summary: {
        totalTimers: this.timers.size,
        intervals: intervals.length,
        timeouts: timeouts.length,
        longRunning: longRunning.length,
        highFrequency: highFrequency.length
      },
      bySource: Object.fromEntries(
        Array.from(bySource.entries()).map(([source, timers]) => [
          source,
          {
            count: timers.length,
            intervals: timers.filter(t => t.type === 'interval').length,
            timeouts: timers.filter(t => t.type === 'timeout').length,
            avgDelay: Math.round(timers.reduce((sum, t) => sum + t.delay, 0) / timers.length)
          }
        ])
      ),
      longRunning: longRunning.map(t => ({
        id: t.id,
        source: t.source,
        delay: t.delay,
        ageMinutes: Math.round((now - t.createdAt) / 60000),
        callback: t.callback
      })),
      highFrequency: highFrequency.map(t => ({
        id: t.id,
        source: t.source,
        delay: t.delay,
        callback: t.callback
      })),
      recommendations: this.getRecommendations(bySource, longRunning, highFrequency)
    }

    return report
  }

  private getRecommendations(
    bySource: Map<string, TimerInfo[]>,
    longRunning: TimerInfo[],
    highFrequency: TimerInfo[]
  ): string[] {
    const recommendations = []

    // Check for excessive timers by source
    bySource.forEach((timers, source) => {
      if (timers.length > 5) {
        recommendations.push(`${source}: Consider consolidating ${timers.length} timers`)
      }
    })

    // Check for high-frequency polling
    if (highFrequency.length > 3) {
      recommendations.push(`${highFrequency.length} high-frequency timers detected - consider using WebSockets or longer intervals`)
    }

    // Check for long-running timers
    if (longRunning.length > 0) {
      recommendations.push(`${longRunning.length} long-running timers - check for memory leaks`)
    }

    // Specific recommendations
    const socketTimers = bySource.get('Socket.io')?.length || 0
    if (socketTimers > 2) {
      recommendations.push('Socket.io: Multiple timers detected - ensure proper cleanup on disconnect')
    }

    const backgroundTasks = bySource.get('Background Tasks')?.length || 0
    if (backgroundTasks > 5) {
      recommendations.push('Background Tasks: Consider using a single task manager with multiple callbacks')
    }

    return recommendations
  }

  printReport() {
    const report = this.getReport()
    
    console.group('🔍 Timer Usage Audit Report')
    console.log('📊 Summary:', report.summary)
    console.log('📋 By Source:', report.bySource)
    
    if (report.longRunning.length > 0) {
      console.warn('⏰ Long-running timers:', report.longRunning)
    }
    
    if (report.highFrequency.length > 0) {
      console.warn('⚡ High-frequency timers:', report.highFrequency)
    }
    
    if (report.recommendations.length > 0) {
      console.warn('💡 Recommendations:')
      report.recommendations.forEach(rec => console.warn(`  - ${rec}`))
    }
    
    console.groupEnd()
    
    return report
  }

  clearAllTimers() {
    console.warn('🚨 Emergency: Clearing all tracked timers')
    
    this.timers.forEach((timer, id) => {
      if (timer.type === 'interval') {
        this.originalClearInterval(id)
      } else {
        this.originalClearTimeout(id)
      }
    })
    
    this.timers.clear()
    console.log('✅ All timers cleared')
  }

  stopMonitoring() {
    if (!this.isMonitoring) return
    
    // Restore original functions
    window.setInterval = this.originalSetInterval
    window.setTimeout = this.originalSetTimeout
    window.clearInterval = this.originalClearInterval
    window.clearTimeout = this.originalClearTimeout
    
    this.isMonitoring = false
    console.log('🛑 Timer audit monitoring stopped')
  }
}

// Create global instance
const timerAudit = new TimerAudit()

// Auto-start in development
if (import.meta.env.DEV) {
  timerAudit.startMonitoring()
  
  // Add to window for debugging
  ;(window as any).timerAudit = timerAudit
  
  // Print report every 2 minutes
  setInterval(() => {
    timerAudit.printReport()
  }, 2 * 60 * 1000)
}

export default timerAudit
