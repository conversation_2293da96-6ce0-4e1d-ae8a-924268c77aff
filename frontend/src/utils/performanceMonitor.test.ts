/**
 * Performance Monitor Test
 * 
 * Simple test to verify the performance monitor loads without errors
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'

describe('Performance Monitor', () => {
  let originalSetInterval: typeof setInterval
  let originalClearInterval: typeof clearInterval
  let originalSetTimeout: typeof setTimeout
  let originalClearTimeout: typeof clearTimeout

  beforeEach(() => {
    // Store original functions
    originalSetInterval = window.setInterval
    originalClearInterval = window.clearInterval
    originalSetTimeout = window.setTimeout
    originalClearTimeout = window.clearTimeout
  })

  afterEach(() => {
    // Restore original functions
    window.setInterval = originalSetInterval
    window.clearInterval = originalClearInterval
    window.setTimeout = originalSetTimeout
    window.clearTimeout = originalClearTimeout
  })

  it('should load without throwing errors', async () => {
    expect(async () => {
      const { performanceMonitor } = await import('./performanceMonitor')
      expect(performanceMonitor).toBeDefined()
    }).not.toThrow()
  })

  it('should initialize with proper context binding', async () => {
    const { performanceMonitor } = await import('./performanceMonitor')
    
    // Test that the monitor can start without throwing "Illegal invocation"
    expect(() => {
      performanceMonitor.startMonitoring(1000)
    }).not.toThrow()

    // Clean up
    performanceMonitor.stopMonitoring()
  })

  it('should handle setInterval interception', async () => {
    const { performanceMonitor } = await import('./performanceMonitor')
    
    let callbackExecuted = false
    const intervalId = setInterval(() => {
      callbackExecuted = true
    }, 100)

    // Wait a bit for the callback to execute
    await new Promise(resolve => setTimeout(resolve, 150))

    clearInterval(intervalId)
    expect(callbackExecuted).toBe(true)
  })

  it('should provide monitoring status', async () => {
    const { performanceMonitor } = await import('./performanceMonitor')
    
    const status = performanceMonitor.getMonitoringStatus()
    expect(status).toHaveProperty('isMonitoring')
    expect(status).toHaveProperty('metricsCount')
    expect(status).toHaveProperty('activeIntervals')
    expect(status).toHaveProperty('activeTimeouts')
  })

  it('should handle memory usage calculation', async () => {
    const { performanceMonitor } = await import('./performanceMonitor')
    
    // Test that memory report doesn't throw errors
    expect(() => {
      const report = performanceMonitor.getMemoryReport()
      expect(report).toHaveProperty('memory')
      expect(report).toHaveProperty('intervals')
      expect(report).toHaveProperty('timeouts')
    }).not.toThrow()
  })

  it('should handle missing performance.memory gracefully', async () => {
    // Mock performance.memory as undefined
    const originalPerformance = (performance as any).memory
    delete (performance as any).memory

    const { performanceMonitor } = await import('./performanceMonitor')
    
    expect(() => {
      const report = performanceMonitor.getMemoryReport()
      expect(report.memory).toEqual({ used: 0, total: 0, percentage: 0 })
    }).not.toThrow()

    // Restore
    if (originalPerformance) {
      (performance as any).memory = originalPerformance
    }
  })
})

// Integration test for App.vue usage
describe('Performance Monitor Integration', () => {
  it('should load lazily without errors', async () => {
    // Simulate the lazy loading from App.vue
    expect(async () => {
      const { performanceMonitor } = await import('./performanceMonitor')
      
      if (!performanceMonitor.isMonitoringActive) {
        performanceMonitor.startMonitoring()
      }
      
      // Should not throw
      expect(performanceMonitor.isMonitoringActive).toBe(true)
      
      // Clean up
      performanceMonitor.stopMonitoring()
    }).not.toThrow()
  })

  it('should handle auto-start in development mode', async () => {
    // The module auto-starts in development, so just importing should work
    expect(async () => {
      await import('./performanceMonitor')
    }).not.toThrow()
  })
})
