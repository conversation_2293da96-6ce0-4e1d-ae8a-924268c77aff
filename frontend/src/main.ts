import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@vueuse/head'

import App from './App.vue'
import router from './router'
import i18n from './i18n'
import { useAuthStore } from './stores/auth'

// Socket.io will be lazy loaded when needed

// Import analytics
// import { analytics } from './services/analytics'

// Firebase Analytics will be lazy loaded when needed

const app = createApp(App)
const pinia = createPinia()
const head = createHead()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(head)
// Socket.io plugin will be loaded lazily

// Initialize auth state and mount app
const initializeApp = async () => {
  const authStore = useAuthStore()

  // Initialize auth state before mounting
  try {
    await authStore.initializeAuth()
  } catch (error) {
    console.warn('Auth initialization failed:', error)
  }

  // Lazy load Socket.io after initial render
  setTimeout(async () => {
    try {
      const { default: socketPlugin } = await import('./plugins/socket')
      app.use(socketPlugin)
      console.log('🔌 Socket.io loaded lazily')
    } catch (error) {
      console.warn('Failed to load Socket.io:', error)
    }
  }, 1000) // Load after 1 second

  app.mount('#app')
}

// Start the app
initializeApp()

// Import development utilities only in dev mode (lazy loaded)
if (import.meta.env.DEV) {
  // Lazy load dev utilities to not block main thread
  setTimeout(() => {
    import('@/utils/pwaTest')
    import('@/utils/testPWAUpdate')
    import('@/utils/memoryLeakDetector')
  }, 2000) // Load after 2 seconds
}
