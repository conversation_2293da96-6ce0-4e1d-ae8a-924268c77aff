import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { useHead } from '@vueuse/head'

export function useSEO() {
  const { t, locale } = useI18n()
  const route = useRoute()

  // Language mapping for proper locale codes
  const localeMap = {
    en: 'en_US',
    es: 'es_ES',
    pt: 'pt_PT'
  }

  // Get current locale code
  const currentLocale = computed(() => localeMap[locale.value as keyof typeof localeMap] || 'en_US')

  // Base URL - should be configured based on environment
  const baseUrl = computed(() => {
    if (typeof window !== 'undefined') {
      return window.location.origin
    }
    return 'https://hlenergy.com' // fallback for SSR
  })

  // Generate alternate URLs for different languages
  const alternateUrls = computed(() => {
    const currentPath = route.path
    return {
      en: `${baseUrl.value}/en${currentPath}`,
      es: `${baseUrl.value}/es${currentPath}`,
      pt: `${baseUrl.value}/pt${currentPath}`,
      default: `${baseUrl.value}${currentPath}`
    }
  })

  // Reactive SEO data
  const seoData = computed(() => ({
    title: t('seo.title'),
    description: t('seo.description'),
    keywords: t('seo.keywords'),
    author: t('seo.author'),
    ogTitle: t('seo.og_title'),
    ogDescription: t('seo.og_description'),
    twitterTitle: t('seo.twitter_title'),
    twitterDescription: t('seo.twitter_description')
  }))

  // Structured data
  const structuredData = computed(() => ({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'HLenergy',
    description: seoData.value.description,
    url: baseUrl.value,
    logo: `${baseUrl.value}/hl-energy-logo-256w.png`,
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+351912345678',
      contactType: 'customer service',
      availableLanguage: ['English', 'Spanish', 'Portuguese']
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Portugal',
      addressCountry: 'PT'
    },
    foundingDate: '2008',
    numberOfEmployees: '50+',
    industry: 'Energy Consulting',
    serviceArea: {
      '@type': 'Place',
      name: 'Worldwide'
    },
    offers: {
      '@type': 'Service',
      name: 'Energy Consultation Services',
      description: seoData.value.description,
      provider: {
        '@type': 'Organization',
        name: 'HLenergy'
      }
    }
  }))

  // Use VueUse Head for reactive SEO management
  const { head } = useHead({
    title: computed(() => seoData.value.title),
    htmlAttrs: {
      lang: locale
    },
    meta: computed(() => [
      // Basic meta tags
      { name: 'description', content: seoData.value.description },
      { name: 'keywords', content: seoData.value.keywords },
      { name: 'author', content: seoData.value.author },
      { name: 'robots', content: 'index, follow' },
      { name: 'language', content: locale.value },
      { 'http-equiv': 'content-language', content: locale.value },
      { name: 'geo.region', content: 'PT' },
      { name: 'geo.placename', content: 'Portugal' },
      { name: 'geo.position', content: '39.3999;-8.2245' },
      { name: 'ICBM', content: '39.3999, -8.2245' },

      // Open Graph meta tags
      { property: 'og:title', content: seoData.value.ogTitle },
      { property: 'og:description', content: seoData.value.ogDescription },
      { property: 'og:type', content: 'website' },
      { property: 'og:url', content: `${baseUrl.value}${route.path}` },
      { property: 'og:site_name', content: 'HLenergy' },
      { property: 'og:locale', content: currentLocale.value },
      { property: 'og:locale:alternate', content: 'en_US' },
      { property: 'og:locale:alternate', content: 'es_ES' },
      { property: 'og:locale:alternate', content: 'pt_PT' },
      { property: 'og:image', content: `${baseUrl.value}/hl-energy-logo-256w.png` },
      { property: 'og:image:width', content: '256' },
      { property: 'og:image:height', content: '256' },
      { property: 'og:image:alt', content: 'HLenergy Logo' },

      // Twitter Card meta tags
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: seoData.value.twitterTitle },
      { name: 'twitter:description', content: seoData.value.twitterDescription },
      { name: 'twitter:image', content: `${baseUrl.value}/hl-energy-logo-256w.png` },
      { name: 'twitter:image:alt', content: 'HLenergy Logo' },
      { name: 'twitter:creator', content: '@HLenergy' },
      { name: 'twitter:site', content: '@HLenergy' },

      // PWA meta tags
      { name: 'theme-color', content: '#02342b' },
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'apple-mobile-web-app-title', content: 'HLenergy' },
      { name: 'mobile-web-app-capable', content: 'yes' },
      { name: 'application-name', content: 'HLenergy' }
    ]),
    link: computed(() => [
      // Canonical URL
      { rel: 'canonical', href: `${baseUrl.value}${route.path}` },

      // Language alternates
      { rel: 'alternate', hreflang: 'en', href: alternateUrls.value.en },
      { rel: 'alternate', hreflang: 'es', href: alternateUrls.value.es },
      { rel: 'alternate', hreflang: 'pt', href: alternateUrls.value.pt },
      { rel: 'alternate', hreflang: 'x-default', href: alternateUrls.value.default },

      // Preconnect for performance
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' }
    ]),
    script: computed(() => [
      {
        type: 'application/ld+json',
        children: JSON.stringify(structuredData.value)
      }
    ])
  })

  // Page-specific SEO updates
  const updatePageSEO = (pageData: {
    title?: string
    description?: string
    keywords?: string
    type?: string
  }) => {
    // Update the head with page-specific data
    head.title = pageData.title || seoData.value.title

    // Add or update page-specific meta tags
    if (pageData.description) {
      const metaArray = Array.isArray(head.meta) ? head.meta : []
      const descIndex = metaArray.findIndex(meta => meta.name === 'description')
      if (descIndex >= 0) {
        metaArray[descIndex].content = pageData.description
      }
    }

    if (pageData.type) {
      const metaArray = Array.isArray(head.meta) ? head.meta : []
      const ogTypeIndex = metaArray.findIndex(meta => meta.property === 'og:type')
      if (ogTypeIndex >= 0) {
        metaArray[ogTypeIndex].content = pageData.type
      }
    }
  }

  // Simple update function for backward compatibility
  const updateSEO = (pageTitle?: string, pageDescription?: string, pageKeywords?: string) => {
    if (pageTitle || pageDescription || pageKeywords) {
      updatePageSEO({
        title: pageTitle,
        description: pageDescription,
        keywords: pageKeywords
      })
    }
  }

  // Generate breadcrumb structured data
  const generateBreadcrumbStructuredData = (breadcrumbs: { name: string; url: string }[]) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: `${baseUrl.value}${crumb.url}`
      }))
    }
  }

  // Generate FAQ structured data
  const generateFAQStructuredData = (faqs: { question: string; answer: string }[]) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer
        }
      }))
    }
  }

  // Generate service structured data
  const generateServiceStructuredData = (service: {
    name: string
    description: string
    provider?: string
    areaServed?: string
    serviceType?: string
  }) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: service.name,
      description: service.description,
      provider: {
        '@type': 'Organization',
        name: service.provider || 'HLenergy'
      },
      areaServed: service.areaServed || 'Worldwide',
      serviceType: service.serviceType || 'Energy Consulting'
    }
  }

  return {
    updateSEO,
    updatePageSEO,
    currentLocale,
    alternateUrls,
    seoData,
    head,
    generateBreadcrumbStructuredData,
    generateFAQStructuredData,
    generateServiceStructuredData
  }
}
