<template>
  <div class="min-h-screen bg-base-200 flex items-center justify-center p-4">
    <div class="card w-full max-w-md bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title text-center mb-6">🔍 Debug Login</h2>
        
        <!-- Environment Info -->
        <div class="mb-4 p-3 bg-info/10 rounded-lg">
          <h3 class="font-semibold text-sm mb-2">Environment Info:</h3>
          <div class="text-xs space-y-1">
            <div>API Base URL: {{ apiBaseUrl }}</div>
            <div>Full Login URL: {{ fullLoginUrl }}</div>
            <div>Current Origin: {{ currentOrigin }}</div>
          </div>
        </div>

        <!-- Test Form -->
        <form @submit.prevent="testLogin" class="space-y-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Email</span>
            </label>
            <input
              v-model="credentials.email"
              type="email"
              class="input input-bordered"
              required
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Password</span>
            </label>
            <input
              v-model="credentials.password"
              type="password"
              class="input input-bordered"
              required
            />
          </div>

          <div class="form-control mt-6">
            <button
              type="submit"
              class="btn btn-primary"
              :class="{ loading: isLoading }"
              :disabled="isLoading"
            >
              {{ isLoading ? 'Testing...' : 'Test Login' }}
            </button>
          </div>
        </form>

        <!-- Test Results -->
        <div v-if="testResult" class="mt-6 p-3 rounded-lg" :class="testResult.success ? 'bg-success/10' : 'bg-error/10'">
          <h3 class="font-semibold text-sm mb-2">
            {{ testResult.success ? '✅ Success' : '❌ Error' }}
          </h3>
          <pre class="text-xs overflow-auto max-h-40">{{ JSON.stringify(testResult.data, null, 2) }}</pre>
        </div>

        <!-- Network Test -->
        <div class="mt-4">
          <button
            @click="testNetworkConnection"
            class="btn btn-outline btn-sm w-full"
            :class="{ loading: isTestingNetwork }"
            :disabled="isTestingNetwork"
          >
            {{ isTestingNetwork ? 'Testing...' : 'Test Network Connection' }}
          </button>
        </div>

        <!-- Clear Cache -->
        <div class="mt-2">
          <button
            @click="clearAllCache"
            class="btn btn-outline btn-warning btn-sm w-full"
          >
            🗑️ Clear All Cache
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { apiService } from '@/services/api'
import axios from 'axios'

const authStore = useAuthStore()

const credentials = ref({
  email: '<EMAIL>',
  password: 'admin123456'
})

const isLoading = ref(false)
const isTestingNetwork = ref(false)
const testResult = ref<any>(null)

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
const fullLoginUrl = computed(() => `${apiBaseUrl}/api/v1/auth/login`)
const currentOrigin = window.location.origin

const testLogin = async () => {
  try {
    isLoading.value = true
    testResult.value = null
    
    console.log('🔍 [DEBUG] Starting login test...')
    
    // Test using auth store
    const response = await authStore.login(credentials.value)
    
    testResult.value = {
      success: true,
      method: 'authStore.login',
      data: response
    }
    
  } catch (error: any) {
    console.error('🔍 [DEBUG] Login test failed:', error)
    
    testResult.value = {
      success: false,
      method: 'authStore.login',
      data: {
        message: error.message,
        status: error.status,
        code: error.code,
        type: error.type,
        stack: error.stack
      }
    }
  } finally {
    isLoading.value = false
  }
}

const testNetworkConnection = async () => {
  try {
    isTestingNetwork.value = true
    
    console.log('🔍 [DEBUG] Testing network connection...')
    
    // Test direct axios call
    const response = await axios.post(fullLoginUrl.value, credentials.value, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    })
    
    testResult.value = {
      success: true,
      method: 'direct axios',
      data: response.data
    }
    
  } catch (error: any) {
    console.error('🔍 [DEBUG] Network test failed:', error)
    
    testResult.value = {
      success: false,
      method: 'direct axios',
      data: {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        code: error.code
      }
    }
  } finally {
    isTestingNetwork.value = false
  }
}

const clearAllCache = async () => {
  try {
    // Clear localStorage
    localStorage.clear()
    
    // Clear sessionStorage
    sessionStorage.clear()
    
    // Clear service worker cache if available
    if ('serviceWorker' in navigator && 'caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
    }
    
    alert('✅ All cache cleared! Please refresh the page.')
    
  } catch (error) {
    console.error('Failed to clear cache:', error)
    alert('❌ Failed to clear some cache. Check console for details.')
  }
}
</script>
