<template>
  <div class="dashboard-view min-h-screen bg-gradient-to-br from-base-100 via-base-200/30 to-base-300/20">
    <!-- Enhanced Dashboard Header -->
    <div class="glass-header bg-base-100/80 backdrop-blur-md border-b border-base-300/50 sticky top-0 z-40">
      <div class="container mx-auto px-4 lg:px-6 max-w-7xl">
        <div class="flex items-center justify-between h-16 lg:h-20">
          <!-- Left Section -->
          <div class="flex items-center space-x-3 lg:space-x-6 xl:space-x-8 flex-1 min-w-0">
            <!-- Mobile Menu Button -->
            <button
              @click="showMobileMenu = !showMobileMenu"
              class="btn btn-ghost btn-sm btn-circle lg:hidden"
              aria-label="Toggle mobile menu"
            >
              <svg v-if="!showMobileMenu" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <!-- Dashboard Title (Mobile) -->
            <div class="lg:hidden flex-1 min-w-0">
              <h1 class="text-lg font-bold text-base-content truncate">Dashboard</h1>
            </div>

            <!-- Back to Home Button (Desktop) -->
            <RouterLink to="/" class="btn btn-ghost btn-sm hidden lg:flex items-center space-x-2 hover:bg-base-200/50">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
              </svg>
              <span>Back to Site</span>
            </RouterLink>

            <!-- Enhanced Navigation (Desktop) -->
            <nav class="hidden lg:flex space-x-1 overflow-x-auto flex-shrink-0">
              <button
                v-for="tab in navigationTabs"
                :key="tab.id"
                @click="activeTab = tab.id; analytics.trackTabChange(tab.id)"
                :class="[
                  'relative px-3 xl:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 whitespace-nowrap flex-shrink-0',
                  activeTab === tab.id
                    ? 'text-primary bg-primary/10 shadow-md'
                    : 'text-base-content/70 hover:text-primary hover:bg-base-200/50'
                ]"
              >
                <span class="flex items-center space-x-1 xl:space-x-2">
                  <svg class="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                    <path :d="tab.iconPath" />
                  </svg>
                  <span class="hidden xl:inline">{{ tab.label }}</span>
                  <span class="xl:hidden">{{ tab.label.split(' ')[0] }}</span>
                </span>
                <div
                  v-if="activeTab === tab.id"
                  class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-gradient-to-r from-primary to-secondary rounded-full"
                ></div>
              </button>
            </nav>
          </div>

          <!-- Right Section -->
          <div class="flex items-center space-x-2 lg:space-x-3 xl:space-x-4 flex-shrink-0">
            <!-- Quick Stats Badge (Desktop only) -->
            <div class="hidden 2xl:flex items-center space-x-2 px-3 py-1.5 bg-success/10 text-success rounded-full text-xs font-medium whitespace-nowrap">
              <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
              <span>{{ dashboardStats?.leads?.total || totalLeads }} {{ $t('leads.leads').toLowerCase() }}</span>
            </div>

            <!-- Theme Toggle -->
            <button
              @click="toggleTheme"
              class="btn btn-ghost btn-sm btn-circle hover:bg-base-200/50"
              title="Toggle theme"
            >
              <svg v-if="isDarkTheme" class="w-4 h-4 lg:w-5 lg:h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
              </svg>
              <svg v-else class="w-4 h-4 lg:w-5 lg:h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
              </svg>
            </button>

            <!-- User Profile with Dropdown -->
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="flex items-center space-x-1 lg:space-x-2 xl:space-x-3 cursor-pointer hover:bg-base-200/50 rounded-lg p-1.5 lg:p-2 transition-colors">
                <div class="hidden xl:block text-right min-w-0">
                  <div class="text-sm font-medium text-base-content truncate max-w-[120px]">{{ authStore.user?.name }}</div>
                  <div class="text-xs text-base-content/60 truncate">{{ authStore.user?.role || 'User' }}</div>
                </div>
                <div class="avatar flex-shrink-0">
                  <div class="w-8 h-8 lg:w-10 lg:h-10 rounded-full bg-gradient-to-br from-primary to-secondary text-primary-content flex items-center justify-center text-xs lg:text-sm font-bold shadow-lg ring-2 ring-primary/20">
                    {{ authStore.userInitials }}
                  </div>
                </div>
                <svg class="w-3 h-3 lg:w-4 lg:h-4 text-base-content/60 hidden xl:block flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 10l5 5 5-5z"/>
                </svg>
              </div>
              <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-xl border border-base-300/50">
                <li>
                  <RouterLink to="/profile" class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <span>Profile</span>
                  </RouterLink>
                </li>
                <li>
                  <RouterLink to="/" class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                    <span>Home</span>
                  </RouterLink>
                </li>
                <div class="divider my-1"></div>
                <li>
                  <button @click="logout" class="flex items-center space-x-2 text-error hover:bg-error/10">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                    <span>Logout</span>
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Dropdown -->
        <div v-if="showMobileMenu" class="lg:hidden border-t border-base-300/50 bg-base-100/95 backdrop-blur-sm">
          <div class="px-4 py-4 space-y-4">
            <!-- User Info (Mobile) -->
            <div class="flex items-center space-x-3 p-3 bg-base-200/50 rounded-lg">
              <div class="avatar">
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary text-primary-content flex items-center justify-center text-sm font-bold">
                  {{ authStore.userInitials }}
                </div>
              </div>
              <div>
                <div class="text-sm font-medium text-base-content">{{ authStore.user?.name }}</div>
                <div class="text-xs text-base-content/60">{{ authStore.user?.role || 'User' }}</div>
              </div>
            </div>

            <!-- Quick Stats (Mobile) -->
            <div class="grid grid-cols-2 gap-3">
              <div class="text-center p-3 bg-success/10 text-success rounded-lg">
                <div class="text-lg font-bold">{{ dashboardStats?.leads?.total || totalLeads }}</div>
                <div class="text-xs">{{ t('dashboard.stats.active_leads') }}</div>
              </div>
              <div class="text-center p-3 bg-info/10 text-info rounded-lg">
                <div class="text-lg font-bold">{{ analyticsStore.analyticsMetrics?.pageViews || 0 }}</div>
                <div class="text-xs">{{ t('dashboard.stats.page_views') }}</div>
              </div>
            </div>

            <!-- Navigation Tabs (Mobile) -->
            <div class="space-y-2">
              <button
                v-for="tab in navigationTabs"
                :key="tab.id"
                @click="activeTab = tab.id; analytics.trackTabChange(tab.id); showMobileMenu = false"
                :class="[
                  'w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300',
                  activeTab === tab.id
                    ? 'text-primary bg-primary/10 shadow-md border border-primary/20'
                    : 'text-base-content/70 hover:text-primary hover:bg-base-200/50'
                ]"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path :d="tab.iconPath" />
                </svg>
                <span>{{ tab.label }}</span>
                <div v-if="activeTab === tab.id" class="ml-auto w-2 h-2 bg-primary rounded-full"></div>
              </button>
            </div>

            <!-- Quick Actions (Mobile) -->
            <div class="space-y-2 pt-2 border-t border-base-300/50">
              <RouterLink
                to="/"
                @click="showMobileMenu = false"
                class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium text-base-content/70 hover:text-primary hover:bg-base-200/50 transition-all duration-300"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span>Back to Site</span>
              </RouterLink>
              <RouterLink
                to="/profile"
                @click="showMobileMenu = false"
                class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium text-base-content/70 hover:text-primary hover:bg-base-200/50 transition-all duration-300"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                <span>Profile</span>
              </RouterLink>
              <button
                @click="logout; showMobileMenu = false"
                class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium text-error hover:bg-error/10 transition-all duration-300"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                </svg>
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Tab Navigation (when menu is closed) -->
        <div v-if="!showMobileMenu" class="lg:hidden mt-4 px-4">
          <div class="flex space-x-1 overflow-x-auto pb-2">
            <button
              v-for="tab in navigationTabs"
              :key="tab.id"
              @click="activeTab = tab.id; analytics.trackTabChange(tab.id)"
              :class="[
                'flex-shrink-0 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 whitespace-nowrap',
                activeTab === tab.id
                  ? 'text-primary bg-primary/10'
                  : 'text-base-content/70 hover:text-primary hover:bg-base-200/50'
              ]"
            >
              <span class="flex items-center space-x-2">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path :d="tab.iconPath" />
                </svg>
                <span>{{ tab.label }}</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Dashboard Content -->
    <div class="container mx-auto px-4 lg:px-6 py-4 lg:py-8">
      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-8">
        <!-- Welcome Section -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h2 class="text-2xl lg:text-3xl font-bold text-base-content">
              {{ t('dashboard.welcome') }}, {{ authStore.user?.name?.split(' ')[0] || 'User' }}! 👋
            </h2>
            <p class="text-base-content/70 mt-1">{{ t('dashboard.overview_description', 'Here\'s what\'s happening with your energy management today.') }}</p>
          </div>
          <div class="flex items-center space-x-3">
            <div class="text-sm text-base-content/60">
              {{ new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) }}
            </div>
          </div>
        </div>

        <!-- Enhanced Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          <!-- Total Leads Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-glow">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 4v4h4V4h-4zm-2-2h8v8h-8V2zM4 4v4h4V4H4zM2 2h8v8H2V2zm2 12v4h4v-4H4zm-2-2h8v8H2v-8zm12 0v2h2v-2h-2zm0 4v2h2v-2h-2zm2-6v2h2v-2h-2zm0 4v2h2v-2h-2zm2-4v2h2v-2h-2z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">{{ t('dashboard.stats.total_leads') }}</h3>
                    </div>
                    <div class="text-3xl font-bold text-primary mb-1">{{ dashboardStats?.leads?.total || totalLeads }}</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">+12%</span>
                      <span class="text-xs text-base-content/60">{{ t('dashboard.vs_last_month', 'vs last month') }}</span>
                    </div>
                  </div>
                  <div class="text-primary/30 group-hover:text-primary/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Conversion Rate Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-glow-sage">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-secondary/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">{{ t('dashboard.stats.conversion_rate') }}</h3>
                    </div>
                    <div class="text-3xl font-bold text-secondary mb-1">{{ (dashboardStats?.performance?.conversionRate || conversionRate).toFixed(1) }}%</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">+2.4%</span>
                      <span class="text-xs text-base-content/60">{{ t('dashboard.vs_last_month') }}</span>
                    </div>
                  </div>
                  <div class="text-secondary/30 group-hover:text-secondary/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M7 14l5-5 5 5z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Active Users Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-accent/5 to-accent/10 border border-accent/20 hover:border-accent/40 transition-all duration-300 hover:shadow-glow-gold">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-accent/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">{{ t('dashboard.stats.active_users', 'Active Users') }}</h3>
                    </div>
                    <div class="text-3xl font-bold text-accent mb-1">{{ dashboardStats?.users?.total || totalUsers }}</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">+8.2%</span>
                      <span class="text-xs text-base-content/60">{{ t('dashboard.last_7_days', 'last 7 days') }}</span>
                    </div>
                  </div>
                  <div class="text-accent/30 group-hover:text-accent/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Qualified Leads Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-success/5 to-success/10 border border-success/20 hover:border-success/40 transition-all duration-300 hover:shadow-lg">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-success/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-success" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">{{ t('dashboard.stats.qualified_leads', 'Qualified Leads') }}</h3>
                    </div>
                    <div class="text-3xl font-bold text-success mb-1">{{ dashboardStats?.leads?.qualified || qualifiedLeads }}</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">{{ t('dashboard.ready', 'Ready') }}</span>
                      <span class="text-xs text-base-content/60">{{ t('dashboard.for_contact', 'for contact') }}</span>
                    </div>
                  </div>
                  <div class="text-success/30 group-hover:text-success/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M5 13l4 4L19 7"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Activity Section -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <!-- Recent Leads -->
          <div class="xl:col-span-2">
            <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
              <div class="card-body p-6">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-base-content">{{ $t('dashboard.recent_leads') }}</h3>
                  </div>
                  <button @click="activeTab = 'leads'" class="btn btn-primary btn-sm">
                    View All
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 5l7 7-7 7"/>
                    </svg>
                  </button>
                </div>

                <div class="space-y-4">
                  <div v-for="(lead, i) in recentLeads" :key="i"
                       class="group p-4 bg-gradient-to-r from-base-200/50 to-base-200/30 rounded-xl border border-base-300/30 hover:border-primary/30 transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                          <span class="text-sm font-bold text-primary">{{ lead.initials }}</span>
                        </div>
                        <div>
                          <div class="font-semibold text-base-content group-hover:text-primary transition-colors">
                            {{ lead.name }}
                          </div>
                          <div class="text-sm text-base-content/70">{{ lead.service }}</div>
                          <div class="text-xs text-base-content/50">{{ lead.email }}</div>
                        </div>
                      </div>
                      <div class="text-right space-y-1">
                        <div class="text-sm text-base-content/70">{{ lead.timeAgo }}</div>
                        <div class="badge badge-primary badge-sm">{{ lead.status }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Top Services -->
          <div class="xl:col-span-1">
            <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
              <div class="card-body p-6">
                <div class="flex items-center space-x-3 mb-6">
                  <div class="w-10 h-10 rounded-lg bg-secondary/10 flex items-center justify-center">
                    <svg class="w-5 h-5 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold text-base-content">Top Services</h3>
                </div>

                <div class="space-y-4">
                  <div v-for="(service, index) in topServices" :key="service.name"
                       class="group p-3 rounded-lg hover:bg-base-200/50 transition-all duration-300">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-lg flex items-center justify-center text-xs font-bold"
                             :class="getServiceBadgeClass(index)">
                          {{ index + 1 }}
                        </div>
                        <span class="font-medium text-base-content">{{ service.name }}</span>
                      </div>
                      <span class="text-sm font-bold text-primary">{{ service.count }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="flex-1 bg-base-300 rounded-full h-2">
                        <div
                          class="h-2 rounded-full transition-all duration-500 ease-out"
                          :class="getServiceProgressClass(index)"
                          :style="{ width: `${service.percentage}%` }"
                        ></div>
                      </div>
                      <span class="text-xs text-base-content/60">{{ service.percentage }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Quick Actions -->
          <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
            <div class="card-body p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-10 h-10 rounded-lg bg-accent/10 flex items-center justify-center">
                  <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-base-content">Quick Actions</h3>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <button class="btn btn-primary btn-sm justify-start" @click="activeTab = 'leads'">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  {{ $t('leads.new_lead') }}
                </button>
                <button class="btn btn-secondary btn-sm justify-start" @click="activeTab = 'analytics'">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                  </svg>
                  View Analytics
                </button>
                <button class="btn btn-accent btn-sm justify-start" @click="exportReport">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z"/>
                  </svg>
                  Export Report
                </button>
                <button class="btn btn-info btn-sm justify-start" @click="activeTab = 'crm'">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                  </svg>
                  CRM Dashboard
                </button>
              </div>
            </div>
          </div>

          <!-- System Status -->
          <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
            <div class="card-body p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-10 h-10 rounded-lg bg-success/10 flex items-center justify-center">
                  <svg class="w-5 h-5 text-success" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-base-content">System Status</h3>
              </div>

              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">API Status</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success">Online</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">Database</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success">Connected</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">Email Service</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success">Active</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">Cache</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-warning rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-warning">Optimizing</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div v-if="activeTab === 'analytics'">
        <AnalyticsDashboard />
      </div>

      <!-- Leads Tab -->
      <div v-if="activeTab === 'leads'" class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold">{{ showAllLeads ? $t('leads.all_leads', 'Todos os Leads') : $t('dashboard.recent_leads') }}</h2>
            <p class="text-sm text-base-content/60">
              {{ showAllLeads ? $t('leads.table.showing_all', `A mostrar ${recentLeads.length} leads`) : $t('leads.table.showing_recent', `A mostrar os últimos ${Math.min(recentLeads.length, 10)} de ${recentLeads.length} leads`) }}
            </p>
          </div>
          <div class="flex gap-2">
            <button @click="toggleViewAllLeads" class="btn btn-outline">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path v-if="!showAllLeads" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path v-if="!showAllLeads" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
              </svg>
              {{ showAllLeads ? $t('leads.show_recent_only', 'Mostrar Apenas Recentes') : $t('leads.view_all_leads', 'Ver Todos os Leads') }}
            </button>
            <button @click="exportLeads" class="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              {{ $t('leads.export_leads', 'Exportar Leads') }}
            </button>
          </div>
        </div>
        
        <div class="card glass-effect bg-base-100/90 backdrop-blur-sm border border-base-300/50 shadow-xl">
          <div class="card-body p-0">
            <div class="w-full">
              <table class="table w-full table-fixed">
                <thead class="bg-gradient-to-r from-base-200/80 to-base-300/60">
                  <tr class="border-b border-base-300/50">
                    <th class="bg-transparent font-semibold text-base-content/80 py-4 px-4 w-1/4">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-primary flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        <span class="truncate">{{ t('leads.table.headers.name') }}</span>
                      </div>
                    </th>
                    <th class="bg-transparent font-semibold text-base-content/80 py-4 px-4 w-1/4">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-secondary flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <span class="truncate">{{ t('leads.table.headers.email') }}</span>
                      </div>
                    </th>
                    <th class="bg-transparent font-semibold text-base-content/80 py-4 px-4 w-1/6 hidden lg:table-cell">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-accent flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        <span class="truncate">{{ t('leads.table.headers.service_interest') }}</span>
                      </div>
                    </th>
                    <th class="bg-transparent font-semibold text-base-content/80 py-4 px-4 w-1/6">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-info flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <span class="truncate">{{ t('leads.table.headers.status') }}</span>
                      </div>
                    </th>
                    <th class="bg-transparent font-semibold text-base-content/80 py-4 px-4 w-1/6 hidden md:table-cell">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-warning flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7v2H3V4h3.5l1-1h5l1 1H17z"/>
                        </svg>
                        <span class="truncate">{{ t('leads.table.headers.created_at') }}</span>
                      </div>
                    </th>
                    <th class="bg-transparent font-semibold text-base-content/80 py-4 px-4 w-1/12 text-center">
                      <div class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-success" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="recentLeads.length === 0">
                    <td colspan="6" class="text-center text-base-content/60 py-12">
                      <div class="flex flex-col items-center gap-4">
                        <div class="w-16 h-16 rounded-full bg-base-200/50 flex items-center justify-center">
                          <svg class="w-8 h-8 text-base-content/40" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                          </svg>
                        </div>
                        <div class="text-center">
                          <div class="font-medium text-base-content/70 mb-1">{{ t('leads.table.no_leads') }}</div>
                          <div class="text-sm text-base-content/50">{{ t('leads.table.no_leads_description') }}</div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr v-else v-for="lead in (showAllLeads ? recentLeads : recentLeads.slice(0, 10))" :key="lead.id"
                      @click="openLeadModal(lead.id)"
                      class="border-b border-base-200/50 hover:bg-gradient-to-r hover:from-base-200/30 hover:to-base-200/10 cursor-pointer transition-all duration-200 group">

                    <!-- Name Column -->
                    <td class="py-3 px-4">
                      <div class="flex items-center gap-3">
                        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center flex-shrink-0">
                          <span class="text-xs font-bold text-primary">{{ lead.initials }}</span>
                        </div>
                        <div class="min-w-0 flex-1">
                          <div class="font-semibold text-base-content group-hover:text-primary transition-colors truncate">{{ lead.name }}</div>
                          <div class="text-xs text-base-content/60 truncate">{{ t('leads.lead') }}</div>
                        </div>
                      </div>
                    </td>

                    <!-- Email Column -->
                    <td class="py-3 px-4">
                      <div class="flex items-center gap-2 min-w-0">
                        <svg class="w-4 h-4 text-base-content/40 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <span class="text-sm text-base-content/80 truncate">{{ lead.email }}</span>
                      </div>
                    </td>

                    <!-- Service Interest Column (Hidden on mobile/tablet) -->
                    <td class="py-3 px-4 hidden lg:table-cell">
                      <div class="text-sm text-base-content/80 truncate">{{ lead.service }}</div>
                    </td>

                    <!-- Status Column -->
                    <td class="py-3 px-4">
                      <div class="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border"
                           :class="getStatusClasses(lead.status)">
                        <div class="w-1.5 h-1.5 rounded-full" :class="getStatusDotClasses(lead.status)"></div>
                        <span class="truncate">{{ lead.status }}</span>
                      </div>
                    </td>

                    <!-- Created At Column (Hidden on mobile) -->
                    <td class="py-3 px-4 hidden md:table-cell">
                      <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-base-content/40 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7v2H3V4h3.5l1-1h5l1 1H17z"/>
                        </svg>
                        <span class="text-sm text-base-content/70 truncate">{{ lead.timeAgo }}</span>
                      </div>
                    </td>

                    <!-- Actions Column -->
                    <td class="py-3 px-4 text-center" @click.stop>
                      <button @click="openLeadModal(lead.id)"
                              class="btn btn-xs btn-ghost hover:btn-primary transition-all duration-200 group/btn">
                        <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                        <span class="hidden sm:inline ml-1">{{ t('leads.actions.view') }}</span>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Reports Tab -->
      <div v-if="activeTab === 'reports'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">{{ t('dashboard.tabs.reports') }}</h2>
          <button @click="exportReport" class="btn btn-primary">{{ t('dashboard.export') }}</button>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Monthly Performance</h3>
              <div class="text-center py-8">
                <div class="text-4xl font-bold text-primary">{{ conversionRate.toFixed(1) }}%</div>
                <div class="text-lg">Average Conversion Rate</div>
                <div class="text-sm text-base-content/70 mt-2">
                  Based on last 30 days of data
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">{{ t('dashboard.revenue_forecast', 'Revenue Forecast') }}</h3>
              <div class="text-center py-8">
                <div class="text-4xl font-bold text-success">${{ (totalLeads * 2500).toLocaleString() }}</div>
                <div class="text-lg">{{ t('dashboard.stats.potential_revenue') }}</div>
                <div class="text-sm text-base-content/70 mt-2">
                  {{ t('dashboard.stats.estimated_based_on') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CRM Tab -->
      <div v-if="activeTab === 'crm'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">{{ t('dashboard.tabs.crm') }}</h2>
          <RouterLink to="/crm/dashboard" class="btn btn-primary">{{ t('dashboard.open_crm', 'Open CRM') }}</RouterLink>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <!-- CRM Quick Access -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">{{ t('dashboard.quick_actions') }}</h3>
              <div class="space-y-3">
                <RouterLink to="/crm/customers" class="btn btn-block btn-sm btn-primary">
                  👥 {{ t('dashboard.manage_customers', 'Manage Customers') }}
                </RouterLink>
                <RouterLink to="/crm/projects" class="btn btn-block btn-sm btn-secondary">
                  📋 {{ t('dashboard.view_projects', 'View Projects') }}
                </RouterLink>
                <RouterLink to="/crm/communications" class="btn btn-block btn-sm btn-accent">
                  💬 {{ t('dashboard.communication_hub', 'Communication Hub') }}
                </RouterLink>
                <RouterLink to="/crm/customers/new" class="btn btn-block btn-sm btn-info">
                  + {{ t('dashboard.add_new_customer', 'Add New Customer') }}
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- CRM Stats -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">{{ t('dashboard.crm_overview', 'CRM Overview') }}</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span>{{ t('dashboard.stats.customers') }}</span>
                  <span class="font-bold">{{ totalCustomers || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>{{ t('dashboard.stats.projects') }}</span>
                  <span class="font-bold">{{ activeProjects?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>{{ t('dashboard.unread_messages', 'Unread Messages') }}</span>
                  <span class="font-bold text-warning">{{ unreadCommunications?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>{{ t('dashboard.stats.conversion_rate') }}</span>
                  <span class="font-bold text-success">{{ conversionRate.toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent CRM Activity -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Recent Activity</h3>
              <div class="space-y-3">
                <div class="text-sm text-base-content/70">
                  No recent CRM activity
                </div>
                <RouterLink to="/crm/communications" class="btn btn-sm btn-ghost">
                  View All Activity
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications Tab -->
      <div v-if="activeTab === 'notifications'" class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold">Push Notifications</h2>
            <p class="text-base-content/70 mt-1">Manage your notification preferences and settings</p>
          </div>
        </div>

        <NotificationManager />
      </div>

      <!-- Cache Tab -->
      <div v-if="activeTab === 'cache'">
        <CacheManagement />
      </div>
    </div>

    <!-- Floating Theme Toggle (Demo) -->
    <div class="fixed bottom-6 right-6 z-50">
      <button
        @click="toggleTheme"
        class="btn btn-circle btn-lg bg-gradient-to-br from-primary to-secondary text-primary-content shadow-2xl hover:shadow-glow transition-all duration-300 hover:scale-110"
        title="Toggle Theme"
      >
        <svg v-if="isDarkTheme" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
        </svg>
        <svg v-else class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Lead Details Modal -->
  <LeadDetailsModal
    :is-open="isLeadModalOpen"
    :lead-id="selectedLeadId"
    @close="closeLeadModal"
    @updated="onLeadUpdated"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, shallowRef, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { useAnalyticsStore } from '@/stores/analytics'
import { useCRMStore } from '@/stores/crm'
import { crmService } from '@/services/crm'
import { contactService } from '@/services/contact'
import { adminService } from '@/services/admin'
import LeadDetailsModal from '@/components/admin/LeadDetailsModal.vue'

import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard.vue'
import CacheManagement from '@/components/CacheManagement.vue'
import NotificationManager from '@/components/notifications/NotificationManager.vue'
import HomeIcon from '@/components/icons/HomeIcon.vue'
import ChartBarIcon from '@/components/icons/ChartBarIcon.vue'
import UserGroupIcon from '@/components/icons/UserGroupIcon.vue'

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const analyticsStore = useAnalyticsStore()
const crmStore = useCRMStore()

// Performance optimization: Disable analytics in development
const analytics = {
  trackClick: (element: any, category = 'dashboard') => {
    // Only track in production to reduce CPU usage
    if (import.meta.env.PROD) {
      // Lazy load analytics to avoid blocking main thread
      import('@/plugins/firebase').then(({ trackEvent }) => {
        trackEvent('click', {
          element,
          category,
          page: 'dashboard'
        })
      }).catch(() => {
        // Silently fail if analytics unavailable
      })
    }
  },
  trackTabChange: (tabName: any) => {
    if (import.meta.env.PROD) {
      import('@/plugins/firebase').then(({ trackEvent }) => {
        trackEvent('tab_change', {
          tab_name: tabName,
          page: 'dashboard'
        })
      }).catch(() => {})
    }
  },
  trackFeatureUsage: (feature: any) => {
    if (import.meta.env.PROD) {
      import('@/plugins/firebase').then(({ trackEvent }) => {
        trackEvent('feature_usage', {
          feature_name: feature,
          page: 'dashboard'
        })
      }).catch(() => {})
    }
  }
}

const activeTab = ref('overview')
const isDarkTheme = ref(false)

// Navigation tabs with SVG icons (reactive to language changes)
const navigationTabs = computed(() => [
  {
    id: 'overview',
    label: t('dashboard.tabs.overview'),
    iconPath: 'M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'
  },
  {
    id: 'analytics',
    label: t('dashboard.analytics'),
    iconPath: 'M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9z'
  },
  {
    id: 'leads',
    label: t('dashboard.tabs.leads'),
    iconPath: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
  },
  {
    id: 'reports',
    label: t('dashboard.tabs.reports'),
    iconPath: 'M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z'
  },
  {
    id: 'crm',
    label: t('dashboard.tabs.crm'),
    iconPath: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z'
  },
  {
    id: 'notifications',
    label: t('common.notifications'),
    iconPath: 'M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'
  },
  {
    id: 'cache',
    label: 'Cache',
    iconPath: 'M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z'
  }
])

const { totalUsers, conversionRate, totalLeads, qualifiedLeads } = analyticsStore
const { totalCustomers, activeProjects, unreadCommunications } = crmStore

// Enhanced sample data
const topServices = ref([
  { name: 'Energy Audit', count: 45, percentage: 90 },
  { name: 'Solar Installation', count: 32, percentage: 64 },
  { name: 'Energy Efficiency', count: 28, percentage: 56 },
  { name: 'Monitoring', count: 15, percentage: 30 },
  { name: 'Compliance', count: 12, percentage: 24 }
])

// Performance optimized: Use shallowRef for complex objects/arrays
const recentLeads = shallowRef<any[]>([])

// Dashboard stats state
const dashboardStats = shallowRef<any>(null)
const statsLoading = ref(false)

// Modal state
const isLeadModalOpen = ref(false)
const selectedLeadId = ref<number | null>(null)

// View state
const showAllLeads = ref(false)
const leadsLimit = ref(5) // Default limit for recent leads
const showMobileMenu = ref(false)

// Performance optimization: Timeout references for cleanup
let fetchStatsTimeout: number | null = null
let fetchLeadsTimeout: number | null = null
let cachedNavigationTabs: any[] | null = null

// Fetch dashboard stats
const fetchDashboardStats = async () => {
  try {
    statsLoading.value = true
    const response = await adminService.getUserDashboardStats()
    dashboardStats.value = response
    console.log('📊 Dashboard stats loaded:', response)
  } catch (error: any) {
    console.error('Failed to fetch dashboard stats:', error)
    // Use fallback data if API fails
    dashboardStats.value = {
      users: { total: 0, active: 0 },
      contacts: { total: 0, today: 0 },
      leads: { total: 0, today: 0, qualified: 0 },
      system: { uptime: 0, status: 'unknown', responseTime: 0 },
      performance: { conversionRate: 0, successRate: 0 }
    }
  } finally {
    statsLoading.value = false
  }
}

// Fetch recent leads from contact submissions (since leads are created from contact submissions)
const fetchRecentLeads = async () => {
  try {
    const limit = showAllLeads.value ? 50 : leadsLimit.value // Show more when viewing all
    const response = await contactService.getContactSubmissions({
      limit: limit,
      sortBy: 'created_at',
      sortOrder: 'desc'
    })

    // Handle PaginatedResponse structure
    const submissions = response?.items || []

    if (Array.isArray(submissions)) {
      const mappedLeads = submissions.map(submission => ({
        name: submission.name || 'Unknown',
        email: submission.email || 'No email',
        service: submission.message ? submission.message.substring(0, 50) + '...' : 'General Inquiry',
        timeAgo: formatTimeAgo(submission.created_at || ''),
        status: getContactStatus(submission.status || 'new'),
        initials: getInitials(submission.name || ''),
        id: submission.id
      }))

      recentLeads.value = mappedLeads
    } else {
      console.warn('Expected array of submissions, got:', typeof submissions)
      recentLeads.value = []
    }
  } catch (error: any) {
    console.error('Failed to fetch recent leads:', error)

    // Check if this is an auth error
    if (error.response?.status === 401 || error.message?.includes('Authentication required')) {
      console.warn('🔒 Authentication required for fetching leads - user may need to login')
      // Don't set mock data for auth errors, just leave empty
      recentLeads.value = []
      return
    }

    // For other errors, use mock data as fallback
    recentLeads.value = [
      {
        name: 'Maria Silva',
        email: '<EMAIL>',
        service: 'Energy Audit Inquiry',
        timeAgo: '2h ago',
        status: 'New',
        initials: 'MS',
        id: 1
      },
      {
        name: 'João Santos',
        email: '<EMAIL>',
        service: 'Solar Installation',
        timeAgo: '4h ago',
        status: 'Contacted',
        initials: 'JS',
        id: 2
      }
    ]
  }
}

// Modal functions
const openLeadModal = (leadId: number) => {
  selectedLeadId.value = leadId
  isLeadModalOpen.value = true
}

const closeLeadModal = () => {
  isLeadModalOpen.value = false
  selectedLeadId.value = null
}

const onLeadUpdated = () => {
  // Refresh the leads list when a lead is updated
  fetchRecentLeads()
}

// Export function
const exportLeads = async () => {
  try {
    console.log('📊 Exporting leads...')
    const blob = await contactService.exportContactSubmissions()

    // Create download link
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `leads-export-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    console.log('✅ Export completed')
  } catch (error) {
    console.error('❌ Export failed:', error)
  }
}

// View toggle functions
const toggleViewAllLeads = async () => {
  showAllLeads.value = !showAllLeads.value
  await fetchRecentLeads()
}

// Helper functions
const formatTimeAgo = (dateString: string) => {
  if (!dateString) return 'Unknown'
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  return date.toLocaleDateString()
}

const getContactStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    'new': t('leads.status.new'),
    'in_progress': t('leads.status.contacted'),
    'contacted': t('leads.status.contacted'),
    'resolved': t('leads.status.qualified'),
    'qualified': t('leads.status.qualified'),
    'closed': t('leads.status.closed'),
    'follow_up': t('leads.status.follow_up'),
    'interested': t('leads.status.interested'),
    'not_interested': t('leads.status.not_interested'),
    'callback_requested': t('leads.status.callback_requested'),
    'meeting_scheduled': t('leads.status.meeting_scheduled'),
    'on_hold': t('leads.status.on_hold')
  }
  return statusMap[status] || t('leads.status.new')
}

const getInitials = (fullName: string) => {
  if (!fullName) return 'UN'
  const nameParts = fullName.trim().split(' ')
  const firstName = nameParts[0] || ''
  const lastName = nameParts[1] || ''
  return `${firstName.charAt(0) || ''}${lastName.charAt(0) || ''}`.toUpperCase()
}

// Status styling functions for enhanced UI
const getStatusClasses = (status: string) => {
  const statusMap: Record<string, string> = {
    'Novo': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
    'New': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
    'Contactado': 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800',
    'Contacted': 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800',
    'Qualificado': 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800',
    'Qualified': 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800',
    'Seguimento': 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800',
    'Follow-up': 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800',
    'Interessado': 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800',
    'Interested': 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800',
    'Não Interessado': 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800',
    'Not Interested': 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800',
    'Fechado': 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
    'Closed': 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
    'Em Espera': 'bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-300 dark:border-indigo-800',
    'On Hold': 'bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-300 dark:border-indigo-800'
  }
  return statusMap[status] || 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800'
}

const getStatusDotClasses = (status: string) => {
  const statusMap: Record<string, string> = {
    'Novo': 'bg-blue-500',
    'New': 'bg-blue-500',
    'Contactado': 'bg-yellow-500',
    'Contacted': 'bg-yellow-500',
    'Qualificado': 'bg-green-500',
    'Qualified': 'bg-green-500',
    'Seguimento': 'bg-orange-500',
    'Follow-up': 'bg-orange-500',
    'Interessado': 'bg-purple-500',
    'Interested': 'bg-purple-500',
    'Não Interessado': 'bg-gray-500',
    'Not Interested': 'bg-gray-500',
    'Fechado': 'bg-red-500',
    'Closed': 'bg-red-500',
    'Em Espera': 'bg-indigo-500',
    'On Hold': 'bg-indigo-500'
  }
  return statusMap[status] || 'bg-gray-500'
}

// Utility functions
const getServiceBadgeClass = (index: number) => {
  const classes = [
    'bg-primary/20 text-primary',
    'bg-secondary/20 text-secondary',
    'bg-accent/20 text-accent',
    'bg-success/20 text-success',
    'bg-info/20 text-info'
  ]
  return classes[index] || 'bg-base-300 text-base-content'
}

const getServiceProgressClass = (index: number) => {
  const classes = [
    'bg-gradient-to-r from-primary to-primary-focus',
    'bg-gradient-to-r from-secondary to-secondary-focus',
    'bg-gradient-to-r from-accent to-accent-focus',
    'bg-gradient-to-r from-success to-success-focus',
    'bg-gradient-to-r from-info to-info-focus'
  ]
  return classes[index] || 'bg-base-content'
}

const toggleTheme = () => {
  const html = document.documentElement
  const currentTheme = html.getAttribute('data-theme')
  const newTheme = currentTheme === 'hlenergy-dark' ? 'hlenergy-light' : 'hlenergy-dark'

  html.setAttribute('data-theme', newTheme)
  isDarkTheme.value = newTheme === 'hlenergy-dark'

  // Save theme preference
  localStorage.setItem('theme', newTheme)

  console.log(`🎨 Theme switched to: ${newTheme}`)
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

const exportReport = async () => {
  try {
    const blob = await analyticsStore.exportAnalyticsData('json')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `hlenergy-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export report:', error)
  }
}

// Close mobile menu when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const mobileMenuButton = document.querySelector('[aria-label="Toggle mobile menu"]')
  const mobileMenu = document.querySelector('.lg\\:hidden .border-t')

  if (showMobileMenu.value &&
      !mobileMenuButton?.contains(target) &&
      !mobileMenu?.contains(target)) {
    showMobileMenu.value = false
  }
}

onMounted(async () => {
  // Performance optimization: Only track in production
  if (import.meta.env.PROD) {
    import('@/plugins/firebase').then(({ trackPageView }) => {
      trackPageView('dashboard', 'User Dashboard')
    }).catch(() => {})
  }

  // Performance optimization: Initialize analytics only if needed
  if (import.meta.env.PROD) {
    await analyticsStore.initializeAnalyticsStore()
  }

  // Fetch dashboard stats with debouncing
  await fetchDashboardStats()

  // Fetch recent leads with debouncing
  await fetchRecentLeads()

  // Set initial theme state
  const currentTheme = document.documentElement.getAttribute('data-theme')
  isDarkTheme.value = currentTheme === 'hlenergy-dark'

  // Add event listener for mobile menu
  document.addEventListener('click', handleClickOutside)

  console.log('📊 Dashboard initialized with theme:', currentTheme)
})

// Performance optimization: Cleanup function for unmount
onUnmounted(() => {
  // Remove event listeners
  document.removeEventListener('click', handleClickOutside)

  // Clear any pending timeouts to prevent memory leaks
  if (fetchStatsTimeout) {
    clearTimeout(fetchStatsTimeout)
    fetchStatsTimeout = null
  }
  if (fetchLeadsTimeout) {
    clearTimeout(fetchLeadsTimeout)
    fetchLeadsTimeout = null
  }

  // Clear cached data to free memory
  cachedNavigationTabs = null

  console.log('🧹 Dashboard cleanup completed - memory freed')
})
</script>

<style scoped>
/* Enhanced Dashboard Styles */
.dashboard-view {
  min-height: 100vh;
  transition: all 0.3s ease-in-out;
}

/* Glass Effect Header */
.glass-header {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Glass Effect Cards */
.glass-effect {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

/* Stats Cards Hover Effects */
.stats-card:hover .glass-effect {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Theme-specific backgrounds */
[data-theme="hlenergy-light"] .dashboard-view {
  background: linear-gradient(135deg,
    hsl(var(--b1)) 0%,
    hsl(var(--b2)) 50%,
    hsl(var(--b3)) 100%);
}

[data-theme="hlenergy-dark"] .dashboard-view {
  background: linear-gradient(135deg,
    hsl(var(--b1)) 0%,
    hsl(var(--b2)) 50%,
    hsl(var(--b3)) 100%);
}

/* Enhanced card styles for light theme */
[data-theme="hlenergy-light"] .glass-effect {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(18, 129, 108, 0.1);
  box-shadow: 0 8px 32px rgba(18, 129, 108, 0.1);
}

/* Enhanced card styles for dark theme */
[data-theme="hlenergy-dark"] .glass-effect {
  background: rgba(10, 31, 27, 0.8);
  border: 1px solid rgba(92, 173, 100, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Glow effects */
.hover\:shadow-glow:hover {
  box-shadow: 0 0 20px rgba(18, 129, 108, 0.3);
}

.hover\:shadow-glow-gold:hover {
  box-shadow: 0 0 20px rgba(234, 170, 52, 0.3);
}

.hover\:shadow-glow-sage:hover {
  box-shadow: 0 0 20px rgba(92, 173, 100, 0.3);
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced button hover effects */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Navigation tab active indicator animation */
@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 50%;
    opacity: 1;
  }
}

.nav-indicator {
  animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glass-header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .stats-card .card-body {
    padding: 1rem;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--p) / 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--p) / 0.5);
}

/* Floating theme toggle */
.btn-circle.btn-lg {
  width: 4rem;
  height: 4rem;
}

.btn-circle:hover {
  transform: scale(1.1);
}

/* Enhanced table styling */
.table th {
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
}

.table tbody tr:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Status badge animations */
.inline-flex {
  transition: all 0.2s ease;
}

.inline-flex:hover {
  transform: scale(1.05);
}

/* Avatar hover effects */
.w-10.h-10.rounded-full {
  transition: all 0.3s ease;
}

.group:hover .w-10.h-10.rounded-full {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .stats-card .text-3xl {
    font-size: 1.875rem;
  }

  .card-body {
    padding: 1rem;
  }

  /* Mobile table adjustments */
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }

  .table th div,
  .table td div {
    font-size: 0.875rem;
  }

  /* Ensure table doesn't overflow */
  .table {
    table-layout: fixed;
    width: 100%;
  }

  /* Better text truncation on mobile */
  .table td {
    max-width: 0;
  }

  .table td > div {
    overflow: hidden;
  }
}
</style>
