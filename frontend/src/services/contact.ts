import { apiService, type PaginatedResponse } from './api'

// Contact Types
export interface ContactSubmission {
  id?: number
  name: string
  email: string
  phone?: string
  message: string
  status?: 'new' | 'in_progress' | 'resolved' | 'closed'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  source?: string
  created_at?: string
  updated_at?: string
}

export interface ContactFormData {
  name: string
  email: string
  phone?: string
  message: string
  source?: string
}

export interface ContactResponse {
  submission: ContactSubmission
  emailQueued: boolean
  emailId?: number
  message: string
}

export interface ContactListParams {
  page?: number
  limit?: number
  status?: string
  priority?: string
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// Contact Service
class ContactService {
  // Submit contact form
  async submitContactForm(formData: ContactFormData): Promise<ContactResponse> {
    return await apiService.post<ContactResponse>('/contact/submit', {
      ...formData,
      source: formData.source || 'website',
    })
  }

  // Get contact submissions (admin only)
  async getContactSubmissions(params: ContactListParams = {}): Promise<PaginatedResponse<ContactSubmission>> {
    const response = await apiService.getRaw<PaginatedResponse<ContactSubmission>>('/contact', params)
    // Backend returns data directly in response, not wrapped in data.data
    return response as any
  }

  // Get specific contact submission (admin only)
  async getContactSubmission(id: number): Promise<ContactSubmission> {
    const response = await apiService.getRaw<{ data: ContactSubmission }>(`/contact/${id}`)
    return response.data
  }

  // Update contact submission status (admin only)
  async updateContactSubmission(
    id: number,
    updates: Partial<ContactSubmission>
  ): Promise<ContactSubmission> {
    const response = await apiService.putRaw<{ data: ContactSubmission }>(`/contact/${id}`, updates)
    return response.data
  }

  // Delete contact submission (admin only)
  async deleteContactSubmission(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/contact/${id}`)
  }

  // Get contact statistics (admin only)
  async getContactStats(): Promise<{
    total: number
    byStatus: Record<string, number>
    byPriority: Record<string, number>
    recent: number
    responseTime: {
      average: number
      median: number
    }
  }> {
    return await apiService.get('/contact/stats')
  }

  // Export contact submissions (admin only)
  async exportContactSubmissions(params: ContactListParams = {}): Promise<Blob> {
    try {
      // Get all contact submissions for export
      const response = await apiService.getRaw<PaginatedResponse<ContactSubmission>>('/contact', {
        ...params,
        limit: 1000 // Get a large number for export
      })

      const submissions = response.items || []

      // Create CSV content
      const headers = ['ID', 'Name', 'Email', 'Phone', 'Message', 'Status', 'Created At', 'Updated At']
      const csvContent = [
        headers.join(','),
        ...submissions.map(submission => [
          submission.id,
          `"${submission.name || ''}"`,
          `"${submission.email || ''}"`,
          `"${submission.phone || ''}"`,
          `"${(submission.message || '').replace(/"/g, '""')}"`, // Escape quotes
          submission.status,
          submission.created_at,
          submission.updated_at
        ].join(','))
      ].join('\n')

      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    } catch (error) {
      console.error('Export failed:', error)
      // Fallback: create empty CSV
      const headers = ['ID', 'Name', 'Email', 'Phone', 'Message', 'Status', 'Created At', 'Updated At']
      const csvContent = headers.join(',')
      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    }
  }

  // Bulk update contact submissions (admin only)
  async bulkUpdateSubmissions(
    ids: number[],
    updates: Partial<ContactSubmission>
  ): Promise<{ updated: number; message: string }> {
    return await apiService.post('/contact/bulk-update', {
      ids,
      updates,
    })
  }

  // Add note to contact submission (admin only)
  async addNote(
    id: number,
    note: string
  ): Promise<{ message: string }> {
    return await apiService.post(`/contact/${id}/notes`, {
      note,
    })
  }

  // Assign contact submission to user (admin only)
  async assignSubmission(
    id: number,
    assignedTo: number
  ): Promise<{ message: string }> {
    return await apiService.post(`/contact/${id}/assign`, {
      assignedTo,
    })
  }
}

// Export singleton instance
export const contactService = new ContactService()
export default contactService
