@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for HLenergy */
html, body {
  scroll-behavior: smooth;
  background-color: oklch(var(--b2));
  min-height: 100vh;
}

#app {
  background-color: oklch(var(--b2));
  min-height: 100vh;
}

/* Mobile-first responsive utilities */
.section-padding {
  padding: 2rem 1rem;
}

@media (min-width: 640px) {
  .section-padding {
    padding: 3rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 4rem 2rem;
  }
}

.container-custom {
  margin: 0 auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Mobile-optimized card spacing */
.card-mobile {
  @apply card bg-base-100 shadow-lg;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .card-mobile {
    margin-bottom: 1.5rem;
  }
}

/* Touch-friendly buttons */
.btn-touch {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
}

/* Enhanced DaisyUI card styling */
.card-enhanced {
  @apply card bg-base-100 shadow-xl border border-base-200;
  transition: all 0.3s ease;
}

.card-enhanced:hover {
  @apply shadow-2xl;
  transform: translateY(-2px);
}

.card-enhanced .card-title {
  @apply flex items-center gap-3 text-lg font-semibold mb-4;
}

/* Mobile-optimized navbar */
.navbar-mobile {
  @apply sticky top-0 z-50 bg-base-100/95 backdrop-blur-sm;
}

/* Mobile dropdown improvements */
.dropdown-mobile {
  @apply w-80 max-w-[90vw] p-4 shadow-2xl border border-base-300;
}

.dropdown-mobile .menu-title {
  @apply mb-3 font-semibold text-base-content/80;
}

.dropdown-mobile li a {
  @apply py-3 px-4 rounded-lg transition-all duration-200;
}

.dropdown-mobile li a:hover {
  @apply bg-base-200 transform scale-[1.02];
}

/* Card grid responsive improvements */
.card-grid {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (min-width: 640px) {
  .card-grid {
    @apply gap-6;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

@media (min-width: 1024px) {
  .card-grid {
    @apply gap-8;
  }
}

/* Stats card improvements */
.stats-enhanced {
  @apply stats shadow-xl bg-base-200/50 border border-base-300;
}

.stats-enhanced .stat {
  @apply place-items-center p-6;
}

.stats-enhanced .stat-title {
  @apply text-base-content/70 font-medium;
}

.stats-enhanced .stat-value {
  @apply text-2xl font-bold;
}

.stats-enhanced .stat-desc {
  @apply text-base-content/60 text-sm;
}

/* Mobile-friendly text sizes */
.text-mobile-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 640px) {
  .text-mobile-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .text-mobile-lg {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

/* WhatsApp button positioning adjustments */
/* Move WhatsApp button when reCAPTCHA badge is present */
.grecaptcha-badge ~ * .fixed.bottom-6.right-6,
body:has(.grecaptcha-badge) .fixed.bottom-6.right-6 {
  bottom: 6rem !important; /* Move up to avoid reCAPTCHA badge */
}

/* Alternative: Move WhatsApp button to left when reCAPTCHA is active */
@supports selector(:has(.grecaptcha-badge)) {
  body:has(.grecaptcha-badge) .fixed.bottom-6.right-6 {
    right: 6rem !important; /* Move left to avoid reCAPTCHA badge */
    bottom: 1.5rem !important; /* Adjust bottom position */
  }
}

/* Fallback for browsers without :has() support */
@supports not selector(:has(.grecaptcha-badge)) {
  /* Contact page specific adjustment */
  .contact-page .fixed.bottom-6.right-6 {
    right: 6rem !important;
    bottom: 1.5rem !important;
  }
}

/* Prevent horizontal scroll */
.overflow-x-hidden {
  overflow-x: hidden;
}

/* Mobile grid improvements */
.grid-mobile-friendly {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-mobile-friendly {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-mobile-friendly {
    gap: 2rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Button text overflow fixes */
.btn-responsive {
  @apply flex-nowrap min-h-[2.5rem] sm:min-h-[3rem];
  @apply px-3 sm:px-6 py-2 sm:py-3;
  @apply text-xs sm:text-sm md:text-base;
}

.btn-responsive .btn-text {
  @apply truncate max-w-[8rem] sm:max-w-none;
}

.btn-responsive .btn-icon {
  @apply flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5;
}

/* CTA button specific styles */
.btn-cta {
  @apply btn-responsive;
  @apply font-semibold shadow-lg hover:shadow-xl;
  @apply transition-all duration-300;
}

.btn-cta-lg {
  @apply btn-cta;
  @apply min-h-[3rem] sm:min-h-[3.5rem];
  @apply px-4 sm:px-8 py-3 sm:py-4;
  @apply text-sm sm:text-lg;
}

/* Contact button styles */
.btn-contact {
  @apply btn-cta;
  @apply gap-1 sm:gap-2;
}

.btn-contact .contact-text {
  @apply truncate;
  @apply max-w-[6rem] sm:max-w-none;
}

/* Free assessment button styles */
.btn-assessment {
  @apply btn-cta-lg;
  @apply gap-1 sm:gap-2;
}

.btn-assessment .assessment-text {
  @apply truncate;
  @apply max-w-[10rem] sm:max-w-none;
}

/* Theme-specific text readability improvements */
[data-theme="hlenergy-light"] {
  /* Ensure high contrast for text elements */
  h1, h2, h3, h4, h5, h6 {
    color: #111827 !important; /* Very dark gray for headings */
  }

  p, span, div:not(.btn):not(.badge):not(.alert) {
    color: #374151; /* Dark gray for body text */
  }

  /* Ensure proper contrast on cards and containers */
  .card-body h1, .card-body h2, .card-body h3, .card-body h4, .card-body h5, .card-body h6 {
    color: #111827 !important;
  }

  .card-body p, .card-body span {
    color: #374151;
  }

  /* Hero sections */
  .hero h1, .hero h2, .hero h3, .hero h4, .hero h5, .hero h6 {
    color: inherit !important; /* Let hero sections use their own colors */
  }

  .hero p, .hero span {
    color: inherit; /* Let hero sections use their own colors */
  }
}

[data-theme="hlenergy-dark"] {
  /* Ensure good readability in dark theme */
  h1, h2, h3, h4, h5, h6 {
    color: #f9fafb !important; /* Very light gray for headings */
  }

  p, span, div:not(.btn):not(.badge):not(.alert) {
    color: #e5e7eb; /* Light gray for body text */
  }

  /* Ensure proper contrast on cards and containers */
  .card-body h1, .card-body h2, .card-body h3, .card-body h4, .card-body h5, .card-body h6 {
    color: #f9fafb !important;
  }

  .card-body p, .card-body span {
    color: #e5e7eb;
  }

  /* Hero sections */
  .hero h1, .hero h2, .hero h3, .hero h4, .hero h5, .hero h6 {
    color: inherit !important; /* Let hero sections use their own colors */
  }

  .hero p, .hero span {
    color: inherit; /* Let hero sections use their own colors */
  }
}

/* Global theme transition */
html, body, #app {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

html.theme-transition *, body.theme-transition *, #app.theme-transition * {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}
