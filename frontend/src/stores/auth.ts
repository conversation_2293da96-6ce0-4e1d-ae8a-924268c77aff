import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authService, type User, type LoginRequest, type RegisterRequest } from '@/services/auth'
import { biometricService } from '@/services/biometric'
import { sessionService, type SessionStatus } from '@/services/session'

export const useAuthStore = defineStore('auth', () => {
  // Helper function to get user from localStorage
  const getUserFromStorage = (): User | null => {
    try {
      const userData = localStorage.getItem('auth_user')
      return userData ? JSON.parse(userData) : null
    } catch (error) {
      console.warn('Failed to parse user data from localStorage:', error)
      localStorage.removeItem('auth_user')
      return null
    }
  }

  // State
  const user = ref<User | null>(getUserFromStorage())
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Server-side session state
  const isLocked = ref(false)
  const sessionStatus = ref<SessionStatus | null>(null)
  const activityTracker = ref<(() => void) | null>(null)
  const sessionMonitor = ref<(() => void) | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAuthenticatedAndUnlocked = computed(() => isAuthenticated.value && !isLocked.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isStaff = computed(() => user.value?.role === 'staff' || isAdmin.value)
  const isEmailVerified = computed(() => user.value?.email_verified || false)
  const userInitials = computed(() => {
    if (!user.value) return ''
    const names = user.value.name.split(' ')
    return names.map(name => name.charAt(0).toUpperCase()).join('')
  })

  // Server-side session management functions
  const updateActivity = async () => {
    if (!token.value || !user.value) {
      return
    }

    try {
      await sessionService.updateActivity()
      console.log('🔄 Activity updated on server')
    } catch (error: any) {
      if (sessionService.handleSessionLockResponse(error)) {
        // Session was locked, handle it
        await handleSessionLocked()
      } else {
        console.warn('Failed to update activity:', error.message)
      }
    }
  }

  const checkSessionLock = async () => {
    console.log('🔍 Checking session lock status...')

    if (!token.value) {
      console.log('🚫 No token - skipping session lock check')
      return
    }

    // Don't check session when offline
    if (!navigator.onLine) {
      console.log('🌐 Offline detected - skipping session lock check')
      return
    }

    try {
      console.log('📡 Calling session service to check lock status...')
      const status = await sessionService.checkSessionLock()
      sessionStatus.value = status
      console.log('📊 Session status received:', status)

      if (status.is_locked) {
        console.log('🔒 Session is locked on server - handling lock state')
        await handleSessionLocked()
      } else {
        console.log('🔓 Session is not locked')
        isLocked.value = false
        // If session is not locked but we don't have user data, try to fetch profile
        if (!user.value) {
          console.log('👤 No user data - attempting to fetch profile...')
          try {
            await fetchProfile()
            console.log('✅ Profile fetched after unlock check')
          } catch (err) {
            console.warn('⚠️ Failed to fetch profile after session unlock check:', err)
          }
        }
      }
    } catch (error: any) {
      console.log('❌ Session lock check failed:', error)
      if (error.message === 'Session is locked' || error.status === 423) {
        console.log('🔒 Session lock detected via error response')
        await handleSessionLocked()
      } else {
        console.warn('⚠️ Unexpected error during session lock check:', error.message)
      }
    }
  }

  const handleSessionLocked = async () => {
    isLocked.value = true

    // Stop activity tracking and monitoring
    if (activityTracker.value) {
      activityTracker.value()
      activityTracker.value = null
    }

    if (sessionMonitor.value) {
      sessionMonitor.value()
      sessionMonitor.value = null
    }

    // Emit event for UI
    window.dispatchEvent(new CustomEvent('session-locked'))
  }

  const startSessionTracking = () => {
    if (!token.value || !user.value) {
      return
    }

    // Start activity tracking (update every 2 minutes)
    if (!activityTracker.value) {
      activityTracker.value = sessionService.startActivityTracking(2)
    }

    // Start session monitoring (check every minute)
    if (!sessionMonitor.value) {
      sessionMonitor.value = sessionService.startSessionMonitoring(1)
    }

    console.log('🔄 Session tracking started')
  }

  const stopSessionTracking = () => {
    if (activityTracker.value) {
      activityTracker.value()
      activityTracker.value = null
    }

    if (sessionMonitor.value) {
      sessionMonitor.value()
      sessionMonitor.value = null
    }

    console.log('🛑 Session tracking stopped')
  }

  const lockSession = async (reason: 'inactivity' | 'admin_action' | 'security_breach' | 'manual' = 'manual') => {
    // Don't lock if offline
    if (!navigator.onLine) {
      console.log('🌐 Offline detected - not locking session')
      return
    }

    try {
      await sessionService.lockSession(reason)
      await handleSessionLocked()
      console.log(`🔒 Session locked: ${reason}`)
    } catch (error: any) {
      console.error('Failed to lock session:', error.message)
      // Fallback to local lock
      await handleSessionLocked()
    }
  }

  const unlockSession = async (method: 'pin' | 'biometric', credentials?: any): Promise<boolean> => {
    try {
      let unlockData: any = { method }

      if (method === 'pin') {
        if (!credentials?.pin) {
          throw new Error('PIN is required for PIN unlock')
        }
        unlockData.pin = credentials.pin
      } else if (method === 'biometric') {
        // For biometric, pass the biometric data
        unlockData.biometric_data = credentials?.biometric_data || {}
      }

      // Call server-side unlock
      await sessionService.unlockSession(unlockData)

      // Update local state
      isLocked.value = false

      // Restart session tracking
      startSessionTracking()

      console.log('🔓 Session unlocked successfully')

      // Refresh data after successful unlock
      await refreshDataAfterUnlock()

      return true
    } catch (error: any) {
      console.error('❌ Session unlock failed:', error.message)
      return false
    }
  }

  const checkSessionStatus = async () => {
    try {
      return await sessionService.checkSessionLock()
    } catch (error: any) {
      console.error('Failed to check session status:', error.message)
      return null
    }
  }

  // Smart refresh data after session unlock - try data refresh first, fallback to page refresh
  const refreshDataAfterUnlock = async () => {
    try {
      console.log('🔄 Starting smart refresh after session unlock...')

      // 1. Always try to refresh user profile first
      let profileRefreshSuccess = false
      if (token.value) {
        try {
          console.log('👤 Refreshing user profile...')
          await fetchProfile()
          profileRefreshSuccess = true
          console.log('✅ User profile refreshed successfully')
        } catch (error) {
          console.warn('⚠️ Failed to refresh user profile:', error)
        }
      }

      // 2. Determine refresh strategy based on current route and profile refresh success
      const currentRoute = window.location.pathname
      console.log('📍 Current route:', currentRoute)

      const shouldTryDataRefresh = profileRefreshSuccess && isDataRefreshSupportedRoute(currentRoute)

      if (shouldTryDataRefresh) {
        console.log('✅ Attempting data refresh strategy...')

        // Emit events for component refresh
        console.log('📡 Emitting session-unlocked event for component refresh...')
        window.dispatchEvent(new CustomEvent('session-unlocked', {
          detail: {
            timestamp: new Date().toISOString(),
            userId: user.value?.id,
            refreshStrategy: 'data'
          }
        }))

        // Emit route-specific refresh events
        if (currentRoute.includes('/admin')) {
          console.log('🔧 Emitting admin-data-refresh event...')
          window.dispatchEvent(new CustomEvent('admin-data-refresh'))
        } else if (currentRoute.includes('/dashboard')) {
          console.log('📊 Emitting dashboard-data-refresh event...')
          window.dispatchEvent(new CustomEvent('dashboard-data-refresh'))
        }

        // Give components time to refresh and verify success
        await new Promise(resolve => setTimeout(resolve, 1500))

        const dataRefreshVerified = await verifyDataRefreshSuccess()

        if (dataRefreshVerified) {
          console.log('✅ Data refresh strategy completed successfully')
          return
        } else {
          console.log('⚠️ Data refresh verification failed, will fallback to page refresh')
        }
      } else {
        console.log('⚠️ Data refresh not suitable, will use page refresh')
      }

      // Fallback to page refresh strategy
      console.log('🔄 Using page refresh strategy as fallback...')

      // Emit event indicating page refresh will happen
      window.dispatchEvent(new CustomEvent('session-unlocked', {
        detail: {
          timestamp: new Date().toISOString(),
          userId: user.value?.id,
          refreshStrategy: 'page'
        }
      }))

      // Trigger page refresh after a short delay
      setTimeout(() => {
        console.log('🔄 Executing page refresh...')
        window.location.reload()
      }, 1000)

    } catch (error) {
      console.error('❌ Error during smart refresh after unlock, forcing page refresh:', error)

      // Final fallback - always refresh page if something goes wrong
      setTimeout(() => {
        window.location.reload()
      }, 500)
    }
  }

  // Check if current route supports data refresh
  const isDataRefreshSupportedRoute = (route: string): boolean => {
    const supportedRoutes = [
      '/admin',
      '/dashboard',
      '/profile',
      '/settings',
      '/socket-test'
    ]

    return supportedRoutes.some(supportedRoute => route.includes(supportedRoute))
  }

  // Verify data refresh was successful
  const verifyDataRefreshSuccess = async (): Promise<boolean> => {
    try {
      // Check multiple indicators of successful refresh
      const checks = [
        // Basic page responsiveness
        document.readyState === 'complete',
        // Page is visible
        !document.hidden,
        // Network is available
        navigator.onLine,
        // User data is still available
        !!user.value,
        // Token is still valid
        !!token.value
      ]

      const allChecksPass = checks.every(check => check)

      console.log(`🔍 Data refresh verification checks: ${checks.map(c => c ? '✅' : '❌').join(' ')}`)

      return allChecksPass

    } catch (error) {
      console.warn('⚠️ Could not verify data refresh success:', error)
      return false
    }
  }

  // Verify PIN for session unlock using session service
  const verifyPinForUnlock = async (pin: string): Promise<boolean> => {
    try {
      console.log('🔐 Verifying PIN for session unlock...')
      return await sessionService.verifyPinForUnlock(pin)
    } catch (error: any) {
      console.error('❌ PIN verification failed:', error.message)
      throw error
    }
  }

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTokens = (accessToken: string, refreshTokenValue: string) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    localStorage.setItem('auth_token', accessToken)
    localStorage.setItem('refresh_token', refreshTokenValue)
  }

  const setUser = (userData: User) => {
    user.value = userData
    localStorage.setItem('auth_user', JSON.stringify(userData))
  }

  const clearTokens = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('auth_user')
  }

  const login = async (credentials: LoginRequest) => {
    try {
      console.log('🔍 [AUTH DEBUG] Starting login process:', {
        email: credentials.email,
        passwordLength: credentials.password?.length || 0
      })

      isLoading.value = true
      clearError()

      console.log('🔍 [AUTH DEBUG] Calling authService.login...')
      const response = await authService.login(credentials)

      console.log('🔍 [AUTH DEBUG] Login response received:', {
        success: !!response,
        hasUser: !!response?.user,
        hasAccessToken: !!response?.accessToken,
        hasRefreshToken: !!response?.refreshToken,
        userRole: response?.user?.role
      })

      setUser(response.user)
      setTokens(response.accessToken, response.refreshToken)

      // Start session tracking after successful login
      startSessionTracking()

      console.log('🔍 [AUTH DEBUG] Login completed successfully')
      return response
    } catch (err: any) {
      console.error('🔍 [AUTH DEBUG] Login failed:', {
        error: err,
        message: err.message,
        status: err.status,
        code: err.code,
        type: err.type
      })

      setError(err.message || 'Login failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterRequest) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.register(userData)
      
      setUser(response.user)
      setTokens(response.accessToken, response.refreshToken)

      // Start session tracking after successful registration
      startSessionTracking()

      return response
    } catch (err: any) {
      setError(err.message || 'Registration failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      isLoading.value = true
      
      // Call logout API
      await authService.logout()
    } catch (err) {
      console.warn('Logout API call failed:', err)
    } finally {
      // Clear local state regardless of API call result
      clearTokens() // This already sets user.value = null
      clearError()
      isLoading.value = false

      // Stop session tracking and clear session state
      stopSessionTracking()
      isLocked.value = false
      sessionStatus.value = null
    }
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authService.refreshToken(refreshToken.value)
      token.value = response.token
      localStorage.setItem('auth_token', response.token)

      return response.token
    } catch (err) {
      // Refresh failed, logout user
      await logout()
      throw err
    }
  }

  const fetchProfile = async () => {
    try {
      isLoading.value = true
      clearError()

      const profile = await authService.getProfile()
      setUser(profile)

      return profile
    } catch (err: any) {
      setError(err.message || 'Failed to fetch profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      clearError()

      const updatedUser = await authService.updateProfile(profileData)
      setUser(updatedUser)

      return updatedUser
    } catch (err: any) {
      setError(err.message || 'Failed to update profile')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (token: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.verifyEmail({ token })
      
      // Refresh user profile to get updated email_verified status
      if (user.value) {
        await fetchProfile()
      }

      return response
    } catch (err: any) {
      setError(err.message || 'Email verification failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resendVerification = async () => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.resendEmailVerification()
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification email')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const requestPasswordReset = async (email: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.requestPasswordReset({ email })
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to request password reset')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (token: string, password: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.confirmPasswordReset({ token, password })
      return response
    } catch (err: any) {
      setError(err.message || 'Password reset failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      clearError()

      const response = await authService.changePassword(currentPassword, newPassword)
      return response
    } catch (err: any) {
      setError(err.message || 'Password change failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Admin PIN authentication
  const loginWithPin = async (pin: string) => {
    try {
      isLoading.value = true
      clearError()

      // Get current user ID from stored user data or localStorage
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      const response = await authService.loginWithPin({
        userId: currentUser.id,
        pin,
        deviceFingerprint: await generateDeviceFingerprint()
      })

      setUser(response.user)
      setTokens(response.accessToken, response.refreshToken)

      return { success: true, user: response.user, authMethod: 'pin' }
    } catch (err: any) {
      setError(err.message || 'PIN authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric authentication
  const loginWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric login
        const response = await authService.loginWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-login-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric login
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        setUser(userData)
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric authentication failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric authentication failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Biometric session unlock (requires existing session)
  const unlockWithBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      // Check if user session exists
      const currentUser = user.value
      if (!currentUser) {
        throw new Error('User session not found. Please login with password first.')
      }

      // Check if biometric is available
      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available')
      }

      // Perform biometric authentication
      const credential = await biometricService.authenticate()

      if (credential) {
        // Call the backend API for biometric unlock
        const response = await authService.unlockWithBiometric({
          credentialId: credential.id,
          signature: btoa(JSON.stringify(credential.response)), // Convert to base64
          clientData: btoa(JSON.stringify({
            type: 'webauthn.get',
            challenge: 'admin-unlock-challenge',
            origin: window.location.origin
          })),
          deviceFingerprint: await generateDeviceFingerprint()
        })

        // Handle nested response structure for biometric unlock
        const userData = response.data?.user || response.user
        const accessToken = response.data?.accessToken || response.accessToken
        const refreshToken = response.data?.refreshToken || response.refreshToken

        setUser(userData)
        setTokens(accessToken, refreshToken)

        return { success: true, user: userData, credential, authMethod: 'biometric' }
      } else {
        throw new Error('Biometric unlock failed')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric unlock failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Setup biometric authentication
  const setupBiometric = async () => {
    try {
      isLoading.value = true
      clearError()

      const isAvailable = await biometricService.isAvailable()
      if (!isAvailable) {
        throw new Error('Biometric authentication not available on this device')
      }

      const credential = await biometricService.register()

      // Register the credential with the backend
      const response = await authService.registerBiometric({
        credentialId: credential.id,
        publicKey: btoa(JSON.stringify(credential.response)), // Convert to base64
        deviceType: 'platform',
        deviceName: `${navigator.platform} - ${new Date().toLocaleDateString()}`,
        transport: ['internal'],
        deviceFingerprint: await generateDeviceFingerprint()
      })

      if (response.success) {
        localStorage.setItem('biometric_credential_id', credential.id)
        return { success: true, credential, credentialId: response.credentialId }
      } else {
        throw new Error(response.message || 'Failed to register biometric credential')
      }
    } catch (err: any) {
      setError(err.message || 'Biometric setup failed')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Check biometric capabilities
  const getBiometricCapabilities = async () => {
    try {
      return await biometricService.getCapabilities()
    } catch (err: any) {
      console.warn('Failed to get biometric capabilities:', err)
      return {
        supported: false,
        available: false,
        authenticators: [],
        hasCredentials: false
      }
    }
  }

  // Generate device fingerprint for security
  const generateDeviceFingerprint = async () => {
    // Generate a simple device fingerprint
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('Device fingerprint', 10, 10)

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    // Simple hash
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36)
  }

  // Initialize auth state on app start
  const initializeAuth = async () => {
    console.log('🔄 Initializing auth state...')
    console.log('📊 Initial state:', {
      hasToken: !!token.value,
      hasUser: !!user.value,
      userFromStorage: !!getUserFromStorage()
    })

    // If we have a token but no user data, try to fetch profile
    if (token.value && !user.value) {
      console.log('🔍 Token exists but no user data - fetching profile...')
      try {
        await fetchProfile()
        console.log('✅ Profile fetched successfully')
      } catch (err: any) {
        // Check if this is a session lock error (423)
        if (err.status === 423) {
          console.log('🔒 Profile fetch blocked - session is locked, but token is valid')
          // Don't clear tokens, this is a valid session that's just locked
          // We'll handle the lock state in the session check below
        } else {
          // Token is invalid for other reasons, clear it
          console.log('❌ Profile fetch failed - clearing invalid tokens:', err.message)
          clearTokens()
          return
        }
      }
    }

    // Start session tracking and check lock status if we have authentication data
    if (token.value) {
      console.log('🔐 Checking session lock status...')

      // Start session tracking if we have user data
      if (user.value) {
        console.log('👤 User data available - starting session tracking')
        startSessionTracking()
      } else {
        console.log('⚠️ No user data available - session tracking will be limited')
      }

      // Always check session lock status if we have a token
      await checkSessionLock()
    } else {
      console.log('🚫 No token found - skipping auth initialization')
    }

    // Set up network status listeners
    setupNetworkListeners()
    console.log('✅ Auth initialization completed')
  }

  const setupNetworkListeners = () => {
    // Handle coming back online
    const handleOnline = () => {
      console.log('🌐 Network back online - resuming session timeout')
      if (isAuthenticated.value && !isLocked.value) {
        resetIdleTimeout()
      }
    }

    // Handle going offline
    const handleOffline = () => {
      console.log('🌐 Network offline - pausing session timeout')
      if (idleTimeout.value) {
        clearTimeout(idleTimeout.value)
        idleTimeout.value = null
      }
    }

    // Add event listeners
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden - user might be trying to bypass lock
        console.log('📱 Page hidden - updating last activity')
        updateActivity()
      } else {
        // Page is visible again - check if lock is required
        console.log('📱 Page visible - checking session lock status')
        if (isAuthenticated.value) {
          checkSessionLock()
        }
      }
    }

    // Add visibility listener
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Store cleanup function
    window.authNetworkCleanup = () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }

  return {
    // State
    user,
    token,
    refreshToken,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    isAuthenticatedAndUnlocked,
    isAdmin,
    isStaff,
    isEmailVerified,
    userInitials,

    // Session state
    isLocked: readonly(isLocked),
    sessionStatus: readonly(sessionStatus),

    // Actions
    setError,
    clearError,
    login,
    register,
    logout,
    refreshAccessToken,
    fetchProfile,
    updateProfile,
    verifyEmail,
    resendVerification,
    requestPasswordReset,
    resetPassword,
    changePassword,
    loginWithPin,
    loginWithBiometric,
    unlockWithBiometric,
    setupBiometric,
    getBiometricCapabilities,
    initializeAuth,
    // Session management actions
    updateActivity,
    lockSession,
    unlockSession,
    checkSessionStatus,
    refreshDataAfterUnlock,
    verifyPinForUnlock,
    checkSessionLock,
    startSessionTracking,
    stopSessionTracking,
  }
})
