<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Data Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info { background: #d1ecf1; color: #0c5460; }
        .log-success { background: #d4edda; color: #155724; }
        .log-warning { background: #fff3cd; color: #856404; }
        .log-error { background: #f8d7da; color: #721c24; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.present { background: #d4edda; color: #155724; }
        .status.missing { background: #f8d7da; color: #721c24; }
        .status.unknown { background: #e2e3e5; color: #383d41; }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>User Data Persistence Test</h1>
    <p>This test verifies that user data is properly persisted to localStorage and restored on page refresh.</p>

    <div class="test-section">
        <h2>Current localStorage State</h2>
        <p>
            <strong>auth_token:</strong> 
            <span id="tokenStatus" class="status unknown">Unknown</span>
        </p>
        <p>
            <strong>auth_user:</strong> 
            <span id="userStatus" class="status unknown">Unknown</span>
        </p>
        <p>
            <strong>refresh_token:</strong> 
            <span id="refreshTokenStatus" class="status unknown">Unknown</span>
        </p>
        
        <div class="data-display" id="userDataDisplay">No user data found</div>
    </div>

    <div class="test-section">
        <h2>Test Actions</h2>
        <button onclick="checkLocalStorage()">Check localStorage</button>
        <button onclick="simulateLogin()" class="success">Simulate Login</button>
        <button onclick="simulateLogout()" class="danger">Simulate Logout</button>
        <button onclick="testRefresh()" class="warning">Test Page Refresh</button>
        <button onclick="clearLog()" class="danger">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li>✅ After login: auth_token, auth_user, and refresh_token should be in localStorage</li>
            <li>✅ After refresh: User data should be restored from localStorage</li>
            <li>✅ isAuthenticated should return true even when session is locked (if user data exists)</li>
            <li>❌ Before fix: User data was not persisted, causing redirects on refresh</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Logging function
        function log(level, message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Status update functions
        function updateStatus(elementId, hasData) {
            const statusEl = document.getElementById(elementId);
            if (hasData) {
                statusEl.textContent = 'Present';
                statusEl.className = 'status present';
            } else {
                statusEl.textContent = 'Missing';
                statusEl.className = 'status missing';
            }
        }

        function updateUserDataDisplay(userData) {
            const displayEl = document.getElementById('userDataDisplay');
            if (userData) {
                displayEl.textContent = JSON.stringify(userData, null, 2);
            } else {
                displayEl.textContent = 'No user data found';
            }
        }

        // Test functions
        function checkLocalStorage() {
            log('info', 'Checking localStorage state...');
            
            const authToken = localStorage.getItem('auth_token');
            const authUser = localStorage.getItem('auth_user');
            const refreshToken = localStorage.getItem('refresh_token');
            
            updateStatus('tokenStatus', !!authToken);
            updateStatus('userStatus', !!authUser);
            updateStatus('refreshTokenStatus', !!refreshToken);
            
            let userData = null;
            if (authUser) {
                try {
                    userData = JSON.parse(authUser);
                    log('success', `User data found: ${userData.name} (${userData.email})`);
                } catch (error) {
                    log('error', `Failed to parse user data: ${error.message}`);
                }
            } else {
                log('warning', 'No user data found in localStorage');
            }
            
            updateUserDataDisplay(userData);
            
            log('info', `Token: ${authToken ? 'Present' : 'Missing'}`);
            log('info', `User: ${authUser ? 'Present' : 'Missing'}`);
            log('info', `Refresh Token: ${refreshToken ? 'Present' : 'Missing'}`);
        }

        function simulateLogin() {
            log('info', 'Simulating login...');
            
            // Simulate user data
            const mockUser = {
                id: 5,
                name: 'Test User',
                email: '<EMAIL>',
                role: 'admin',
                email_verified: true
            };
            
            const mockToken = 'mock-jwt-token-' + Date.now();
            const mockRefreshToken = 'mock-refresh-token-' + Date.now();
            
            // Set data in localStorage (simulating what the auth store would do)
            localStorage.setItem('auth_token', mockToken);
            localStorage.setItem('auth_user', JSON.stringify(mockUser));
            localStorage.setItem('refresh_token', mockRefreshToken);
            
            log('success', 'Mock login completed - data stored in localStorage');
            checkLocalStorage();
        }

        function simulateLogout() {
            log('info', 'Simulating logout...');
            
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
            localStorage.removeItem('refresh_token');
            
            log('success', 'Mock logout completed - data cleared from localStorage');
            checkLocalStorage();
        }

        function testRefresh() {
            log('warning', 'Testing page refresh...');
            log('info', 'Current state before refresh:');
            checkLocalStorage();
            
            setTimeout(() => {
                log('info', 'Refreshing page in 2 seconds...');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }, 1000);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('info', 'Test log cleared');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('info', 'User Data Persistence Test initialized');
            checkLocalStorage();
            
            // Check periodically
            setInterval(checkLocalStorage, 5000);
        });

        // Listen for storage changes
        window.addEventListener('storage', function(e) {
            if (e.key && e.key.startsWith('auth_')) {
                log('info', `Storage changed: ${e.key}`);
                checkLocalStorage();
            }
        });
    </script>
</body>
</html>
